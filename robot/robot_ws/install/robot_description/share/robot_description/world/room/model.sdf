<?xml version='1.0'?>
<sdf version='1.7'>
  <model name='room'>
    <pose>-1.215 -2.7325 0 0 -0 0</pose>
    <link name='Wall_0'>
      <collision name='Wall_0_Collision'>
        <geometry>
          <box>
            <size>5 0.15 2.5</size>
          </box>
        </geometry>
        <pose>0 0 1.25 0 -0 0</pose>
      </collision>
      <visual name='Wall_0_Visual'>
        <pose>0 0 1.25 0 -0 0</pose>
        <geometry>
          <box>
            <size>5 0.15 2.5</size>
          </box>
        </geometry>
        <material>
          <script>
            <uri>file://media/materials/scripts/gazebo.material</uri>
            <name>Gazebo/CeilingTiled</name>
          </script>
          <ambient>1 1 1 1</ambient>
        </material>
        <meta>
          <layer>0</layer>
        </meta>
      </visual>
      <pose>-4.675 -0.2325 0 0 -0 -1.5708</pose>
    </link>
    <link name='Wall_10'>
      <collision name='Wall_10_Collision'>
        <geometry>
          <box>
            <size>2 0.15 2.5</size>
          </box>
        </geometry>
        <pose>0 0 1.25 0 -0 0</pose>
      </collision>
      <visual name='Wall_10_Visual'>
        <pose>0 0 1.25 0 -0 0</pose>
        <geometry>
          <box>
            <size>2 0.15 2.5</size>
          </box>
        </geometry>
        <material>
          <script>
            <uri>file://media/materials/scripts/gazebo.material</uri>
            <name>Gazebo/Wood</name>
          </script>
          <ambient>1 1 1 1</ambient>
        </material>
        <meta>
          <layer>0</layer>
        </meta>
      </visual>
      <pose>-1.145 1.2575 0 0 -0 -1.5708</pose>
    </link>
    <link name='Wall_12'>
      <collision name='Wall_12_Collision'>
        <geometry>
          <box>
            <size>2 0.15 2.5</size>
          </box>
        </geometry>
        <pose>0 0 1.25 0 -0 0</pose>
      </collision>
      <visual name='Wall_12_Visual'>
        <pose>0 0 1.25 0 -0 0</pose>
        <geometry>
          <box>
            <size>2 0.15 2.5</size>
          </box>
        </geometry>
        <material>
          <script>
            <uri>file://media/materials/scripts/gazebo.material</uri>
            <name>Gazebo/Wood</name>
          </script>
          <ambient>1 1 1 1</ambient>
        </material>
        <meta>
          <layer>0</layer>
        </meta>
      </visual>
      <pose>0.085 1.2575 0 0 -0 -1.5708</pose>
    </link>
    <link name='Wall_15'>
      <collision name='Wall_15_Collision'>
        <geometry>
          <box>
            <size>2 0.15 2.5</size>
          </box>
        </geometry>
        <pose>0 0 1.25 0 -0 0</pose>
      </collision>
      <visual name='Wall_15_Visual'>
        <pose>0 0 1.25 0 -0 0</pose>
        <geometry>
          <box>
            <size>2 0.15 2.5</size>
          </box>
        </geometry>
        <material>
          <script>
            <uri>file://media/materials/scripts/gazebo.material</uri>
            <name>Gazebo/Wood</name>
          </script>
          <ambient>1 1 1 1</ambient>
        </material>
        <meta>
          <layer>0</layer>
        </meta>
      </visual>
      <pose>1.625 1.2675 0 0 -0 -1.5708</pose>
    </link>
    <link name='Wall_17'>
      <collision name='Wall_17_Collision'>
        <geometry>
          <box>
            <size>2 0.15 2.5</size>
          </box>
        </geometry>
        <pose>0 0 1.25 0 -0 0</pose>
      </collision>
      <visual name='Wall_17_Visual'>
        <pose>0 0 1.25 0 -0 0</pose>
        <geometry>
          <box>
            <size>2 0.15 2.5</size>
          </box>
        </geometry>
        <material>
          <script>
            <uri>file://media/materials/scripts/gazebo.material</uri>
            <name>Gazebo/Wood</name>
          </script>
          <ambient>1 1 1 1</ambient>
        </material>
        <meta>
          <layer>0</layer>
        </meta>
      </visual>
      <pose>3.315 1.2175 0 0 -0 -1.5708</pose>
    </link>
    <link name='Wall_2'>
      <collision name='Wall_2_Collision'>
        <geometry>
          <box>
            <size>9.5 0.15 2.5</size>
          </box>
        </geometry>
        <pose>0 0 1.25 0 -0 0</pose>
      </collision>
      <visual name='Wall_2_Visual'>
        <pose>0 0 1.25 0 -0 0</pose>
        <geometry>
          <box>
            <size>9.5 0.15 2.5</size>
          </box>
        </geometry>
        <material>
          <script>
            <uri>file://media/materials/scripts/gazebo.material</uri>
            <name>Gazebo/CeilingTiled</name>
          </script>
          <ambient>1 1 1 1</ambient>
        </material>
        <meta>
          <layer>0</layer>
        </meta>
      </visual>
      <pose>-0 -2.6575 0 0 -0 0</pose>
    </link>
    <link name='Wall_4'>
      <collision name='Wall_4_Collision'>
        <geometry>
          <box>
            <size>5 0.15 2.5</size>
          </box>
        </geometry>
        <pose>0 0 1.25 0 -0 0</pose>
      </collision>
      <visual name='Wall_4_Visual'>
        <pose>0 0 1.25 0 -0 0</pose>
        <geometry>
          <box>
            <size>5 0.15 2.5</size>
          </box>
        </geometry>
        <material>
          <script>
            <uri>file://media/materials/scripts/gazebo.material</uri>
            <name>Gazebo/CeilingTiled</name>
          </script>
          <ambient>1 1 1 1</ambient>
        </material>
        <meta>
          <layer>0</layer>
        </meta>
      </visual>
      <pose>4.675 -0.2325 0 0 -0 1.5708</pose>
    </link>
    <link name='Wall_6'>
      <collision name='Wall_6_Collision'>
        <geometry>
          <box>
            <size>9.5 0.15 2.5</size>
          </box>
        </geometry>
        <pose>0 0 1.25 0 -0 0</pose>
      </collision>
      <visual name='Wall_6_Visual'>
        <pose>0 0 1.25 0 -0 0</pose>
        <geometry>
          <box>
            <size>9.5 0.15 2.5</size>
          </box>
        </geometry>
        <material>
          <script>
            <uri>file://media/materials/scripts/gazebo.material</uri>
            <name>Gazebo/CeilingTiled</name>
          </script>
          <ambient>1 1 1 1</ambient>
        </material>
        <meta>
          <layer>0</layer>
        </meta>
      </visual>
      <pose>-0 2.1925 0 0 -0 3.14159</pose>
    </link>
    <link name='Wall_8'>
      <collision name='Wall_8_Collision'>
        <geometry>
          <box>
            <size>2 0.15 2.5</size>
          </box>
        </geometry>
        <pose>0 0 1.25 0 -0 0</pose>
      </collision>
      <visual name='Wall_8_Visual'>
        <pose>0 0 1.25 0 -0 0</pose>
        <geometry>
          <box>
            <size>2 0.15 2.5</size>
          </box>
        </geometry>
        <material>
          <script>
            <uri>file://media/materials/scripts/gazebo.material</uri>
            <name>Gazebo/Wood</name>
          </script>
          <ambient>1 1 1 1</ambient>
        </material>
        <meta>
          <layer>0</layer>
        </meta>
      </visual>
      <pose>-3.065 1.2275 0 0 -0 -1.5708</pose>
    </link>
    <static>1</static>
  </model>
</sdf>
