<robot xmlns:xacro="http://www.ros.org/wiki/xacro">
    <link name="laser_rear_link">
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <cylinder length="0.05" radius="0.02" />
            </geometry>
            <material name="green">
                <color rgba="0 1 0 1" />
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <cylinder length="0.05" radius="0.02" />
            </geometry>
        </collision>
        <inertial>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <mass value="0.1" />
            <inertia ixx="0.0001" ixy="0" ixz="0" iyy="0.0001" iyz="0" izz="0.0001" />
        </inertial>
    </link>

    <joint name="laser_rear_joint" type="fixed">
        <parent link="base_link" />
        <child link="laser_rear_link" />
        <origin xyz="-0.260 0 0.07" rpy="0 0 3.14159" /> <!-- 车身顶部后端，旋转180度朝后 -->
    </joint>
</robot>
