controller_manager:
  ros__parameters:
    update_rate: 100  # Hz

    # --- 控制器列表 ---
    # 1. 差速驱动控制器
    diff_drive_controller:
      type: diff_drive_controller/DiffDriveController

    # 2. 关节状态发布器 (确保这个控制器也在列表中)
    joint_state_broadcaster:
      type: joint_state_broadcaster/JointStateBroadcaster

# =======================================================
# ===           差速驱动控制器详细参数配置            ===
# =======================================================
diff_drive_controller:
  ros__parameters:
    # --- 您的原始参数 (完全正确) ---
    publish_rate: 50.0
    base_frame_id: base_footprint
    odom_frame_id: odom
    enable_odom_tf: true
    
    # 注意: diff_drive_controller 是为两轮差速设计的
    # 如果您是四轮滑动转向，使用 skid_steer_drive_controller 会更合适
    # 但如果您的后轮是主驱动，前轮是随动，这样配置也可以工作
    left_wheel_names: ["rear_left_wheel_joint"]
    right_wheel_names: ["rear_right_wheel_joint"]

    wheel_separation: 0.36   # 2 * wheel_offset_y
    wheel_radius: 0.08       # wheel_radius
    
    use_stamped_vel: false   # 监听 geometry_msgs/msg/Twist
    cmd_vel_timeout: 0.5
    
    # ------------------ 新增/修改的关键参数 ------------------
    # 1. 明确指定里程计话题名称，移除自动添加的命名空间
    odom_topic: "odom"

    # 2. 明确指定速度指令话题名称
    #    当 use_stamped_vel 为 false 时，控制器默认会监听 "cmd_vel"
    #    但为了清晰和确保万无一失，我们在这里显式地指定它。
    cmd_vel_topic: "cmd_vel"
    # -----------------------------------------------------------

# =======================================================
# ===         关节状态发布器 (通常无需配置)           ===
# =======================================================
joint_state_broadcaster:
  ros__parameters:
    {}