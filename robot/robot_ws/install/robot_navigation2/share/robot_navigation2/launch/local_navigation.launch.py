import os
import launch
import launch_ros
from ament_index_python.packages import get_package_share_directory
from launch.launch_description_sources import PythonLaunchDescriptionSource


def generate_launch_description():
    # Get package directories
    robot_navigation2_dir = get_package_share_directory('robot_navigation2')
    nav2_bringup_dir = get_package_share_directory('nav2_bringup')
    rviz_config_dir = os.path.join(robot_navigation2_dir, 'config', 'local_nav_rviz.rviz')
    
    # Launch configuration variables
    use_sim_time = launch.substitutions.LaunchConfiguration('use_sim_time', default='true')
    nav2_param_path = launch.substitutions.LaunchConfiguration(
        'params_file', 
        default=os.path.join(robot_navigation2_dir, 'config', 'nav2_params.yaml')
    )
    
    # Lifecycle manager for local navigation nodes
    lifecycle_nodes = [
        'controller_server',
        'planner_server', 
        'behavior_server',
        'bt_navigator',
        'waypoint_follower',
        'velocity_smoother'
    ]

    return launch.LaunchDescription([
        # Declare launch arguments
        launch.actions.DeclareLaunchArgument(
            'use_sim_time', 
            default_value=use_sim_time,
            description='Use simulation (Gazebo) clock if true'
        ),
        launch.actions.DeclareLaunchArgument(
            'params_file', 
            default_value=nav2_param_path,
            description='Full path to param file to load'
        ),

        # Controller server
        launch_ros.actions.Node(
            package='nav2_controller',
            executable='controller_server',
            output='screen',
            parameters=[nav2_param_path],
            remappings=[('/tf', 'tf'), ('/tf_static', 'tf_static')]
        ),

        # Planner server
        launch_ros.actions.Node(
            package='nav2_planner',
            executable='planner_server',
            name='planner_server',
            output='screen',
            parameters=[nav2_param_path],
            remappings=[('/tf', 'tf'), ('/tf_static', 'tf_static')]
        ),

        # Behavior server
        launch_ros.actions.Node(
            package='nav2_behaviors',
            executable='behavior_server',
            name='behavior_server',
            output='screen',
            parameters=[nav2_param_path],
            remappings=[('/tf', 'tf'), ('/tf_static', 'tf_static')]
        ),

        # BT Navigator
        launch_ros.actions.Node(
            package='nav2_bt_navigator',
            executable='bt_navigator',
            name='bt_navigator',
            output='screen',
            parameters=[nav2_param_path],
            remappings=[('/tf', 'tf'), ('/tf_static', 'tf_static')]
        ),

        # Waypoint follower
        launch_ros.actions.Node(
            package='nav2_waypoint_follower',
            executable='waypoint_follower',
            name='waypoint_follower',
            output='screen',
            parameters=[nav2_param_path],
            remappings=[('/tf', 'tf'), ('/tf_static', 'tf_static')]
        ),

        # Velocity smoother
        launch_ros.actions.Node(
            package='nav2_velocity_smoother',
            executable='velocity_smoother',
            name='velocity_smoother',
            output='screen',
            parameters=[nav2_param_path],
            remappings=[('/tf', 'tf'), ('/tf_static', 'tf_static'),
                       ('/cmd_vel_smoothed', '/diff_drive_controller/cmd_vel_unstamped')]
        ),

        # Lifecycle manager
        launch_ros.actions.Node(
            package='nav2_lifecycle_manager',
            executable='lifecycle_manager',
            name='lifecycle_manager_navigation',
            output='screen',
            parameters=[{'use_sim_time': use_sim_time},
                       {'autostart': True},
                       {'node_names': lifecycle_nodes}]
        ),

        # RViz2
        launch_ros.actions.Node(
            package='rviz2',
            executable='rviz2',
            name='rviz2',
            arguments=['-d', rviz_config_dir],
            parameters=[{'use_sim_time': use_sim_time}],
            output='screen'
        ),
    ])
