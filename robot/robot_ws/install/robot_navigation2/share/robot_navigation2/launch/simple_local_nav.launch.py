import os
import launch
import launch_ros
from ament_index_python.packages import get_package_share_directory


def generate_launch_description():
    # Get package directories
    robot_navigation2_dir = get_package_share_directory('robot_navigation2')
    
    # Launch configuration variables
    use_sim_time = launch.substitutions.LaunchConfiguration('use_sim_time', default='true')
    nav2_param_path = launch.substitutions.LaunchConfiguration(
        'params_file', 
        default=os.path.join(robot_navigation2_dir, 'config', 'nav2_params.yaml')
    )
    
    # Minimal lifecycle nodes for local navigation
    lifecycle_nodes = [
        'controller_server',
        'planner_server', 
        'behavior_server',
        'bt_navigator'
    ]

    return launch.LaunchDescription([
        # Declare launch arguments
        launch.actions.DeclareLaunchArgument(
            'use_sim_time', 
            default_value=use_sim_time,
            description='Use simulation (Gazebo) clock if true'
        ),
        launch.actions.DeclareLaunchArgument(
            'params_file', 
            default_value=nav2_param_path,
            description='Full path to param file to load'
        ),

        # Controller server - handles local path following
        launch_ros.actions.Node(
            package='nav2_controller',
            executable='controller_server',
            output='screen',
            parameters=[nav2_param_path],
            remappings=[('/tf', 'tf'), ('/tf_static', 'tf_static')]
        ),

        # Planner server - handles local path planning
        launch_ros.actions.Node(
            package='nav2_planner',
            executable='planner_server',
            name='planner_server',
            output='screen',
            parameters=[nav2_param_path],
            remappings=[('/tf', 'tf'), ('/tf_static', 'tf_static')]
        ),

        # Behavior server - handles recovery behaviors
        launch_ros.actions.Node(
            package='nav2_behaviors',
            executable='behavior_server',
            name='behavior_server',
            output='screen',
            parameters=[nav2_param_path],
            remappings=[('/tf', 'tf'), ('/tf_static', 'tf_static')]
        ),

        # BT Navigator - coordinates navigation behaviors
        launch_ros.actions.Node(
            package='nav2_bt_navigator',
            executable='bt_navigator',
            name='bt_navigator',
            output='screen',
            parameters=[nav2_param_path],
            remappings=[('/tf', 'tf'), ('/tf_static', 'tf_static')]
        ),

        # Lifecycle manager - manages node lifecycle
        launch_ros.actions.Node(
            package='nav2_lifecycle_manager',
            executable='lifecycle_manager',
            name='lifecycle_manager_navigation',
            output='screen',
            parameters=[{'use_sim_time': use_sim_time},
                       {'autostart': True},
                       {'node_names': lifecycle_nodes}]
        ),
    ])
