[0.009s] Invoking command in '/home/<USER>/robot/robot_ws/build/robot_navigation2': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/robot/robot_ws/build/robot_navigation2 -- -j20 -l20
[0.024s] -- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
[0.106s] -- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
[0.151s] -- Added test 'flake8' to check Python code syntax and style conventions
[0.151s] -- Added test 'lint_cmake' to check CMake code style
[0.151s] -- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
[0.152s] -- Added test 'xmllint' to check XML markup files
[0.152s] -- Configuring done
[0.153s] -- Generating done
[0.154s] -- Build files have been written to: /home/<USER>/robot/robot_ws/build/robot_navigation2
[0.170s] Invoked command in '/home/<USER>/robot/robot_ws/build/robot_navigation2' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/robot/robot_ws/build/robot_navigation2 -- -j20 -l20
[0.172s] Invoking command in '/home/<USER>/robot/robot_ws/build/robot_navigation2': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/robot/robot_ws/build/robot_navigation2
[0.177s] -- Install configuration: ""
[0.178s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/launch
[0.178s] -- Installing: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/launch/local_navigation.launch.py
[0.178s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/launch/navigation2.launch.py
[0.178s] -- Installing: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/launch/simple_local_nav.launch.py
[0.178s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/maps
[0.178s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/maps/room.pgm
[0.178s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/maps/room.yaml
[0.178s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/config
[0.178s] -- Installing: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/config/nav2_params.yaml
[0.178s] -- Installing: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/config/local_nav_rviz.rviz
[0.179s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/ament_index/resource_index/package_run_dependencies/robot_navigation2
[0.179s] -- Installing: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/ament_index/resource_index/parent_prefix_path/robot_navigation2
[0.179s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/environment/ament_prefix_path.sh
[0.179s] -- Installing: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/environment/ament_prefix_path.dsv
[0.179s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/environment/path.sh
[0.179s] -- Installing: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/environment/path.dsv
[0.179s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/local_setup.bash
[0.180s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/local_setup.sh
[0.181s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/local_setup.zsh
[0.181s] -- Installing: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/local_setup.dsv
[0.181s] -- Installing: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/package.dsv
[0.181s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/ament_index/resource_index/packages/robot_navigation2
[0.181s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/cmake/robot_navigation2Config.cmake
[0.182s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/cmake/robot_navigation2Config-version.cmake
[0.182s] -- Installing: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/package.xml
[0.182s] Invoked command in '/home/<USER>/robot/robot_ws/build/robot_navigation2' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/robot/robot_ws/build/robot_navigation2
