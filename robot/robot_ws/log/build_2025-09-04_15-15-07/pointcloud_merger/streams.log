[0.010s] Invoking command in '/home/<USER>/robot/robot_ws/build/pointcloud_merger': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/robot/robot_ws/build/pointcloud_merger -- -j20 -l20
[0.046s] [35m[1mConsolidate compiler generated dependencies of target pointcloud_merger_node[0m
[0.046s] [35m[1mConsolidate compiler generated dependencies of target pointcloud_to_laserscan_node[0m
[0.066s] [ 50%] Built target pointcloud_to_laserscan_node
[0.068s] [100%] Built target pointcloud_merger_node
[0.074s] Invoked command in '/home/<USER>/robot/robot_ws/build/pointcloud_merger' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/robot/robot_ws/build/pointcloud_merger -- -j20 -l20
[0.075s] Invoking command in '/home/<USER>/robot/robot_ws/build/pointcloud_merger': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/robot/robot_ws/build/pointcloud_merger
[0.082s] -- Install configuration: ""
[0.082s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/lib/pointcloud_merger/pointcloud_merger_node
[0.082s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/lib/pointcloud_merger/pointcloud_to_laserscan_node
[0.082s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/launch
[0.082s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/launch/merger.launch.py
[0.082s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/ament_index/resource_index/package_run_dependencies/pointcloud_merger
[0.082s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/ament_index/resource_index/parent_prefix_path/pointcloud_merger
[0.082s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/environment/ament_prefix_path.sh
[0.082s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/environment/ament_prefix_path.dsv
[0.082s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/environment/path.sh
[0.082s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/environment/path.dsv
[0.082s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/local_setup.bash
[0.082s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/local_setup.sh
[0.083s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/local_setup.zsh
[0.083s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/local_setup.dsv
[0.083s] -- Installing: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/package.dsv
[0.083s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/ament_index/resource_index/packages/pointcloud_merger
[0.083s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/cmake/pointcloud_mergerConfig.cmake
[0.083s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/cmake/pointcloud_mergerConfig-version.cmake
[0.083s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/package.xml
[0.084s] Invoked command in '/home/<USER>/robot/robot_ws/build/pointcloud_merger' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/robot/robot_ws/build/pointcloud_merger
