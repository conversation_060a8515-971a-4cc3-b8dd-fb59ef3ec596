[0.015s] Invoking command in '/home/<USER>/robot/robot_ws/build/pointcloud_merger': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/robot/robot_ws/build/pointcloud_merger -- -j20 -l20
[0.069s] [100%] Built target pointcloud_merger_node
[0.069s] [100%] Built target pointcloud_to_laserscan_node
[0.094s] Invoked command in '/home/<USER>/robot/robot_ws/build/pointcloud_merger' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/robot/robot_ws/build/pointcloud_merger -- -j20 -l20
[0.095s] Invoking command in '/home/<USER>/robot/robot_ws/build/pointcloud_merger': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/robot/robot_ws/build/pointcloud_merger
[0.102s] -- Install configuration: ""
[0.102s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/lib/pointcloud_merger/pointcloud_merger_node
[0.102s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/lib/pointcloud_merger/pointcloud_to_laserscan_node
[0.102s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/launch
[0.103s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/launch/merger.launch.py
[0.103s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/ament_index/resource_index/package_run_dependencies/pointcloud_merger
[0.103s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/ament_index/resource_index/parent_prefix_path/pointcloud_merger
[0.103s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/environment/ament_prefix_path.sh
[0.103s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/environment/ament_prefix_path.dsv
[0.103s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/environment/path.sh
[0.104s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/environment/path.dsv
[0.104s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/local_setup.bash
[0.104s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/local_setup.sh
[0.104s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/local_setup.zsh
[0.104s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/local_setup.dsv
[0.104s] -- Installing: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/package.dsv
[0.104s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/ament_index/resource_index/packages/pointcloud_merger
[0.104s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/cmake/pointcloud_mergerConfig.cmake
[0.104s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/cmake/pointcloud_mergerConfig-version.cmake
[0.104s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/package.xml
[0.106s] Invoked command in '/home/<USER>/robot/robot_ws/build/pointcloud_merger' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/robot/robot_ws/build/pointcloud_merger
