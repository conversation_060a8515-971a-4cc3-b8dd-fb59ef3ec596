-- The C compiler identification is GNU 11.4.0
-- The CXX compiler identification is GNU 11.4.0
-- Detecting C compiler AB<PERSON> info
-- Detecting C compiler ABI info - done
-- Check for working C compiler: /usr/bin/cc - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler ABI info
-- Detecting CXX compiler ABI info - done
-- Check for working CXX compiler: /usr/bin/c++ - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
-- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter 
-- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
-- Added test 'flake8' to check Python code syntax and style conventions
-- Added test 'lint_cmake' to check CMake code style
-- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
-- Added test 'xmllint' to check XML markup files
-- Configuring done
-- Generating done
-- Build files have been written to: /home/<USER>/robot/robot_ws/build/robot_navigation2
-- Install configuration: ""
-- Installing: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/launch
-- Installing: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/launch/navigation2.launch.py
-- Installing: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/maps
-- Installing: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/maps/room.pgm
-- Installing: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/maps/room.yaml
-- Installing: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/config
-- Installing: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/config/nav2_params.yaml
-- Installing: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/ament_index/resource_index/package_run_dependencies/robot_navigation2
-- Installing: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/ament_index/resource_index/parent_prefix_path/robot_navigation2
-- Installing: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/environment/ament_prefix_path.sh
-- Installing: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/environment/ament_prefix_path.dsv
-- Installing: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/environment/path.sh
-- Installing: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/environment/path.dsv
-- Installing: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/local_setup.bash
-- Installing: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/local_setup.sh
-- Installing: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/local_setup.zsh
-- Installing: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/local_setup.dsv
-- Installing: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/package.dsv
-- Installing: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/ament_index/resource_index/packages/robot_navigation2
-- Installing: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/cmake/robot_navigation2Config.cmake
-- Installing: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/cmake/robot_navigation2Config-version.cmake
-- Installing: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/package.xml
