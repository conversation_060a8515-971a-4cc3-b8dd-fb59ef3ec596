[0.008s] Invoking command in '/home/<USER>/robot/robot_ws/build/robot_navigation2': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/robot/robot_ws/src/robot_navigation2 -DCMAKE_INSTALL_PREFIX=/home/<USER>/robot/robot_ws/install/robot_navigation2
[0.116s] -- The C compiler identification is GNU 11.4.0
[0.174s] -- The CXX compiler identification is GNU 11.4.0
[0.180s] -- Detecting C compiler ABI info
[0.236s] -- Detecting C compiler ABI info - done
[0.241s] -- Check for working C compiler: /usr/bin/cc - skipped
[0.241s] -- Detecting C compile features
[0.241s] -- Detecting C compile features - done
[0.244s] -- Detecting CXX compiler ABI info
[0.302s] -- Detecting CXX compiler ABI info - done
[0.306s] -- Check for working CXX compiler: /usr/bin/c++ - skipped
[0.306s] -- Detecting CXX compile features
[0.306s] -- Detecting CXX compile features - done
[0.314s] -- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
[0.433s] -- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter 
[0.501s] -- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
[0.556s] -- Added test 'flake8' to check Python code syntax and style conventions
[0.556s] -- Added test 'lint_cmake' to check CMake code style
[0.557s] -- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
[0.558s] -- Added test 'xmllint' to check XML markup files
[0.558s] -- Configuring done
[0.560s] -- Generating done
[0.561s] -- Build files have been written to: /home/<USER>/robot/robot_ws/build/robot_navigation2
[0.564s] Invoked command in '/home/<USER>/robot/robot_ws/build/robot_navigation2' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/robot/robot_ws/src/robot_navigation2 -DCMAKE_INSTALL_PREFIX=/home/<USER>/robot/robot_ws/install/robot_navigation2
[0.565s] Invoking command in '/home/<USER>/robot/robot_ws/build/robot_navigation2': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/robot/robot_ws/build/robot_navigation2 -- -j20 -l20
[0.596s] Invoked command in '/home/<USER>/robot/robot_ws/build/robot_navigation2' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/robot/robot_ws/build/robot_navigation2 -- -j20 -l20
[0.602s] Invoking command in '/home/<USER>/robot/robot_ws/build/robot_navigation2': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/robot/robot_ws/build/robot_navigation2
[0.607s] -- Install configuration: ""
[0.607s] -- Installing: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/launch
[0.607s] -- Installing: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/launch/navigation2.launch.py
[0.607s] -- Installing: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/maps
[0.607s] -- Installing: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/maps/room.pgm
[0.607s] -- Installing: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/maps/room.yaml
[0.608s] -- Installing: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/config
[0.608s] -- Installing: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/config/nav2_params.yaml
[0.608s] -- Installing: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/ament_index/resource_index/package_run_dependencies/robot_navigation2
[0.608s] -- Installing: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/ament_index/resource_index/parent_prefix_path/robot_navigation2
[0.608s] -- Installing: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/environment/ament_prefix_path.sh
[0.608s] -- Installing: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/environment/ament_prefix_path.dsv
[0.608s] -- Installing: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/environment/path.sh
[0.608s] -- Installing: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/environment/path.dsv
[0.608s] -- Installing: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/local_setup.bash
[0.608s] -- Installing: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/local_setup.sh
[0.608s] -- Installing: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/local_setup.zsh
[0.608s] -- Installing: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/local_setup.dsv
[0.608s] -- Installing: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/package.dsv
[0.608s] -- Installing: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/ament_index/resource_index/packages/robot_navigation2
[0.608s] -- Installing: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/cmake/robot_navigation2Config.cmake
[0.608s] -- Installing: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/cmake/robot_navigation2Config-version.cmake
[0.608s] -- Installing: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/package.xml
[0.610s] Invoked command in '/home/<USER>/robot/robot_ws/build/robot_navigation2' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/robot/robot_ws/build/robot_navigation2
