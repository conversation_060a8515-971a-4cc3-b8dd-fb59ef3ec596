[0.011s] Invoking command in '/home/<USER>/robot/robot_ws/build/pointcloud_merger': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/robot/robot_ws/src/pointcloud_merger -DCMAKE_INSTALL_PREFIX=/home/<USER>/robot/robot_ws/install/pointcloud_merger
[0.124s] -- The C compiler identification is GNU 11.4.0
[0.180s] -- The CXX compiler identification is GNU 11.4.0
[0.187s] -- Detecting C compiler ABI info
[0.242s] -- Detecting C compiler ABI info - done
[0.246s] -- Check for working C compiler: /usr/bin/cc - skipped
[0.247s] -- Detecting C compile features
[0.248s] -- Detecting C compile features - done
[0.251s] -- Detecting CXX compiler ABI info
[0.308s] -- Detecting CXX compiler ABI info - done
[0.313s] -- Check for working CXX compiler: /usr/bin/c++ - skipped
[0.313s] -- Detecting CXX compile features
[0.313s] -- Detecting CXX compile features - done
[0.320s] -- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
[0.438s] -- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter 
[0.508s] -- Found rclcpp: 16.0.12 (/opt/ros/humble/share/rclcpp/cmake)
[0.550s] -- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.555s] -- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.561s] -- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.573s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.588s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.629s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.631s] -- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[0.700s] -- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.2")  
[0.719s] -- Found FastRTPS: /opt/ros/humble/include  
[0.748s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[0.758s] -- Looking for pthread.h
[0.820s] -- Looking for pthread.h - found
[0.821s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD
[0.872s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
[0.872s] -- Found Threads: TRUE  
[0.917s] -- Found sensor_msgs: 4.8.0 (/opt/ros/humble/share/sensor_msgs/cmake)
[0.946s] -- Found pcl_conversions: 2.4.5 (/opt/ros/humble/share/pcl_conversions/cmake)
[0.980s] -- Checking for module 'eigen3'
[0.989s] --   Found eigen3, version 3.4.0
[1.013s] -- Found Eigen: /usr/include/eigen3 (Required is at least version "3.1") 
[1.013s] -- Eigen found (include: /usr/include/eigen3, version: 3.4.0)
[1.040s] -- Checking for module 'flann'
[1.051s] --   Found flann, version 1.9.1
[1.080s] -- Found FLANN: /usr/lib/x86_64-linux-gnu/libflann_cpp.so  
[1.080s] -- FLANN found (include: /usr/include, lib: /usr/lib/x86_64-linux-gnu/libflann_cpp.so)
[1.790s] -- Checking for module 'libusb-1.0'
[1.801s] --   Found libusb-1.0, version 1.0.25
[1.827s] -- Found libusb: /usr/lib/x86_64-linux-gnu/libusb-1.0.so  
[1.828s] -- OpenNI found (version: 1.5.4.0, include: /usr/include/ni, lib: /usr/lib/libOpenNI.so;libusb::libusb)
[1.870s] -- OpenNI2 found (version: 2.2.0.33, include: /usr/include/openni2, lib: /usr/lib/x86_64-linux-gnu/libOpenNI2.so;libusb::libusb)
[1.978s] -- Eigen found (include: /usr/include/eigen3, version: 3.4.0)
[1.983s] -- OpenNI found (version: 1.5.4.0, include: /usr/include/ni, lib: /usr/lib/libOpenNI.so;libusb::libusb)
[1.989s] -- OpenNI2 found (version: 2.2.0.33, include: /usr/include/openni2, lib: /usr/lib/x86_64-linux-gnu/libOpenNI2.so;libusb::libusb)
[2.087s] -- Found Qhull version 8.0.2
[2.181s] -- OpenNI found (version: 1.5.4.0, include: /usr/include/ni, lib: /usr/lib/libOpenNI.so;libusb::libusb)
[2.276s] -- Found PCL_COMMON: /usr/lib/x86_64-linux-gnu/libpcl_common.so  
[2.276s] -- Found PCL_KDTREE: /usr/lib/x86_64-linux-gnu/libpcl_kdtree.so  
[2.277s] -- Found PCL_OCTREE: /usr/lib/x86_64-linux-gnu/libpcl_octree.so  
[2.277s] -- Found PCL_SEARCH: /usr/lib/x86_64-linux-gnu/libpcl_search.so  
[2.278s] -- Found PCL_SAMPLE_CONSENSUS: /usr/lib/x86_64-linux-gnu/libpcl_sample_consensus.so  
[2.278s] -- Found PCL_FILTERS: /usr/lib/x86_64-linux-gnu/libpcl_filters.so  
[2.279s] -- Found PCL_2D: /usr/include/pcl-1.12  
[2.279s] -- Found PCL_GEOMETRY: /usr/include/pcl-1.12  
[2.279s] -- Found PCL_IO: /usr/lib/x86_64-linux-gnu/libpcl_io.so  
[2.280s] -- Found PCL_FEATURES: /usr/lib/x86_64-linux-gnu/libpcl_features.so  
[2.281s] -- Found PCL_ML: /usr/lib/x86_64-linux-gnu/libpcl_ml.so  
[2.281s] -- Found PCL_SEGMENTATION: /usr/lib/x86_64-linux-gnu/libpcl_segmentation.so  
[2.282s] -- Found PCL_VISUALIZATION: /usr/lib/x86_64-linux-gnu/libpcl_visualization.so  
[2.282s] -- Found PCL_SURFACE: /usr/lib/x86_64-linux-gnu/libpcl_surface.so  
[2.283s] -- Found PCL_REGISTRATION: /usr/lib/x86_64-linux-gnu/libpcl_registration.so  
[2.283s] -- Found PCL_KEYPOINTS: /usr/lib/x86_64-linux-gnu/libpcl_keypoints.so  
[2.284s] -- Found PCL_TRACKING: /usr/lib/x86_64-linux-gnu/libpcl_tracking.so  
[2.284s] -- Found PCL_RECOGNITION: /usr/lib/x86_64-linux-gnu/libpcl_recognition.so  
[2.285s] -- Found PCL_STEREO: /usr/lib/x86_64-linux-gnu/libpcl_stereo.so  
[2.286s] -- Found PCL_APPS: /usr/lib/x86_64-linux-gnu/libpcl_apps.so  
[2.286s] -- Found PCL_IN_HAND_SCANNER: /usr/include/pcl-1.12  
[2.286s] -- Found PCL_MODELER: /usr/include/pcl-1.12  
[2.292s] -- Found PCL_POINT_CLOUD_EDITOR: /usr/include/pcl-1.12  
[2.292s] -- Found PCL_OUTOFCORE: /usr/lib/x86_64-linux-gnu/libpcl_outofcore.so  
[2.293s] -- Found PCL_PEOPLE: /usr/lib/x86_64-linux-gnu/libpcl_people.so  
[2.295s] -- Found pcl_ros: 2.4.5 (/opt/ros/humble/share/pcl_ros/cmake)
[2.325s] -- Found eigen3_cmake_module: 0.1.1 (/opt/ros/humble/share/eigen3_cmake_module/cmake)
[2.327s] -- Found Eigen3: TRUE (found version "3.4.0") 
[2.327s] -- Ensuring Eigen3 include directory is part of orocos-kdl CMake target
[2.395s] -- Found tf2_eigen: 0.25.13 (/opt/ros/humble/share/tf2_eigen/cmake)
[2.398s] -- Found laser_geometry: 2.4.0 (/opt/ros/humble/share/laser_geometry/cmake)
[2.402s] -- Found tf2_sensor_msgs: 0.25.13 (/opt/ros/humble/share/tf2_sensor_msgs/cmake)
[2.405s] -- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
[2.468s] -- Added test 'cppcheck' to perform static code analysis on C / C++ code
[2.468s] -- Configured cppcheck include dirs: 
[2.468s] -- Configured cppcheck exclude dirs and/or files: 
[2.468s] -- Added test 'flake8' to check Python code syntax and style conventions
[2.468s] -- Added test 'lint_cmake' to check CMake code style
[2.468s] -- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
[2.469s] -- Added test 'uncrustify' to check C / C++ code style
[2.469s] -- Configured uncrustify additional arguments: 
[2.469s] -- Added test 'xmllint' to check XML markup files
[2.469s] -- Configuring done
[2.526s] -- Generating done
[2.535s] -- Build files have been written to: /home/<USER>/robot/robot_ws/build/pointcloud_merger
[2.548s] Invoked command in '/home/<USER>/robot/robot_ws/build/pointcloud_merger' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/robot/robot_ws/src/pointcloud_merger -DCMAKE_INSTALL_PREFIX=/home/<USER>/robot/robot_ws/install/pointcloud_merger
[2.550s] Invoking command in '/home/<USER>/robot/robot_ws/build/pointcloud_merger': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/robot/robot_ws/build/pointcloud_merger -- -j20 -l20
[2.590s] [ 25%] [32mBuilding CXX object CMakeFiles/pointcloud_to_laserscan_node.dir/src/pointcloud_to_laserscan_node.cpp.o[0m
[2.590s] [ 50%] [32mBuilding CXX object CMakeFiles/pointcloud_merger_node.dir/src/pointcloud_merger_node.cpp.o[0m
[9.875s] [ 75%] [32m[1mLinking CXX executable pointcloud_to_laserscan_node[0m
[10.544s] [ 75%] Built target pointcloud_to_laserscan_node
[11.806s] [100%] [32m[1mLinking CXX executable pointcloud_merger_node[0m
[12.313s] [100%] Built target pointcloud_merger_node
[12.319s] Invoked command in '/home/<USER>/robot/robot_ws/build/pointcloud_merger' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/robot/robot_ws/build/pointcloud_merger -- -j20 -l20
[12.321s] Invoking command in '/home/<USER>/robot/robot_ws/build/pointcloud_merger': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/robot/robot_ws/build/pointcloud_merger
[12.327s] -- Install configuration: ""
[12.327s] -- Installing: /home/<USER>/robot/robot_ws/install/pointcloud_merger/lib/pointcloud_merger/pointcloud_merger_node
[12.330s] -- Set runtime path of "/home/<USER>/robot/robot_ws/install/pointcloud_merger/lib/pointcloud_merger/pointcloud_merger_node" to ""
[12.330s] -- Installing: /home/<USER>/robot/robot_ws/install/pointcloud_merger/lib/pointcloud_merger/pointcloud_to_laserscan_node
[12.332s] -- Set runtime path of "/home/<USER>/robot/robot_ws/install/pointcloud_merger/lib/pointcloud_merger/pointcloud_to_laserscan_node" to ""
[12.332s] -- Installing: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/launch
[12.332s] -- Installing: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/launch/merger.launch.py
[12.332s] -- Installing: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/ament_index/resource_index/package_run_dependencies/pointcloud_merger
[12.332s] -- Installing: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/ament_index/resource_index/parent_prefix_path/pointcloud_merger
[12.332s] -- Installing: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/environment/ament_prefix_path.sh
[12.332s] -- Installing: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/environment/ament_prefix_path.dsv
[12.332s] -- Installing: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/environment/path.sh
[12.332s] -- Installing: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/environment/path.dsv
[12.332s] -- Installing: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/local_setup.bash
[12.332s] -- Installing: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/local_setup.sh
[12.332s] -- Installing: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/local_setup.zsh
[12.332s] -- Installing: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/local_setup.dsv
[12.332s] -- Installing: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/package.dsv
[12.332s] -- Installing: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/ament_index/resource_index/packages/pointcloud_merger
[12.332s] -- Installing: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/cmake/pointcloud_mergerConfig.cmake
[12.332s] -- Installing: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/cmake/pointcloud_mergerConfig-version.cmake
[12.332s] -- Installing: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/package.xml
[12.334s] Invoked command in '/home/<USER>/robot/robot_ws/build/pointcloud_merger' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/robot/robot_ws/build/pointcloud_merger
