[0.008s] Invoking command in '/home/<USER>/robot/robot_ws/build/robot_description': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/robot/robot_ws/src/robot_description -DCMAKE_INSTALL_PREFIX=/home/<USER>/robot/robot_ws/install/robot_description
[0.119s] -- The C compiler identification is GNU 11.4.0
[0.174s] -- The CXX compiler identification is GNU 11.4.0
[0.182s] -- Detecting C compiler ABI info
[0.239s] -- Detecting C compiler ABI info - done
[0.244s] -- Check for working C compiler: /usr/bin/cc - skipped
[0.244s] -- Detecting C compile features
[0.244s] -- Detecting C compile features - done
[0.246s] -- Detecting CXX compiler ABI info
[0.303s] -- Detecting CXX compiler ABI info - done
[0.309s] -- Check for working CXX compiler: /usr/bin/c++ - skipped
[0.309s] -- Detecting CXX compile features
[0.309s] -- Detecting CXX compile features - done
[0.316s] -- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
[0.433s] -- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter 
[0.504s] -- Found rclcpp: 16.0.12 (/opt/ros/humble/share/rclcpp/cmake)
[0.546s] -- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.550s] -- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.556s] -- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.568s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.583s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.624s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.626s] -- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[0.695s] -- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.2")  
[0.714s] -- Found FastRTPS: /opt/ros/humble/include  
[0.746s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[0.753s] -- Looking for pthread.h
[0.813s] -- Looking for pthread.h - found
[0.813s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD
[0.863s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
[0.864s] -- Found Threads: TRUE  
[0.911s] -- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
[0.957s] -- Added test 'flake8' to check Python code syntax and style conventions
[0.957s] -- Added test 'lint_cmake' to check CMake code style
[0.958s] -- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
[0.958s] -- Added test 'xmllint' to check XML markup files
[0.959s] -- Configuring done
[0.961s] -- Generating done
[0.963s] -- Build files have been written to: /home/<USER>/robot/robot_ws/build/robot_description
[0.968s] Invoked command in '/home/<USER>/robot/robot_ws/build/robot_description' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/robot/robot_ws/src/robot_description -DCMAKE_INSTALL_PREFIX=/home/<USER>/robot/robot_ws/install/robot_description
[0.969s] Invoking command in '/home/<USER>/robot/robot_ws/build/robot_description': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/robot/robot_ws/build/robot_description -- -j20 -l20
[1.002s] Invoked command in '/home/<USER>/robot/robot_ws/build/robot_description' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/robot/robot_ws/build/robot_description -- -j20 -l20
[1.003s] Invoking command in '/home/<USER>/robot/robot_ws/build/robot_description': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/robot/robot_ws/build/robot_description
[1.010s] -- Install configuration: ""
[1.010s] -- Installing: /home/<USER>/robot/robot_ws/install/robot_description/share/ament_index/resource_index/package_run_dependencies/robot_description
[1.010s] -- Installing: /home/<USER>/robot/robot_ws/install/robot_description/share/ament_index/resource_index/parent_prefix_path/robot_description
[1.010s] -- Installing: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/environment/ament_prefix_path.sh
[1.010s] -- Installing: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/environment/ament_prefix_path.dsv
[1.010s] -- Installing: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/environment/path.sh
[1.010s] -- Installing: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/environment/path.dsv
[1.011s] -- Installing: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/local_setup.bash
[1.011s] -- Installing: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/local_setup.sh
[1.011s] -- Installing: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/local_setup.zsh
[1.011s] -- Installing: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/local_setup.dsv
[1.011s] -- Installing: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/package.dsv
[1.011s] -- Installing: /home/<USER>/robot/robot_ws/install/robot_description/share/ament_index/resource_index/packages/robot_description
[1.011s] -- Installing: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/cmake/robot_descriptionConfig.cmake
[1.011s] -- Installing: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/cmake/robot_descriptionConfig-version.cmake
[1.011s] -- Installing: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/package.xml
[1.011s] -- Installing: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/launch
[1.011s] -- Installing: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/launch/gazebo.launch.py
[1.011s] -- Installing: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/urdf
[1.011s] -- Installing: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/urdf/robot
[1.011s] -- Installing: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/urdf/robot/robot.urdf.xacro
[1.011s] -- Installing: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/urdf/robot/actuator
[1.011s] -- Installing: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/urdf/robot/actuator/wheel.urdf.xacro
[1.012s] -- Installing: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/urdf/robot/base.urdf.xacro
[1.012s] -- Installing: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/urdf/robot/sensor
[1.012s] -- Installing: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/urdf/robot/sensor/imu.urdf.xacro
[1.012s] -- Installing: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/urdf/robot/sensor/laser_front.urdf.xacro
[1.012s] -- Installing: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/urdf/robot/sensor/laser_rear.urdf.xacro
[1.012s] -- Installing: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/urdf/robot/sensor/gazebo_sensor_plugin.xacro
[1.012s] -- Installing: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/config
[1.012s] -- Installing: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/config/ros2_control.yaml
[1.013s] -- Installing: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/world
[1.013s] -- Installing: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/world/custom_room.world
[1.013s] -- Installing: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/world/room
[1.013s] -- Installing: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/world/room/model.sdf
[1.015s] Invoked command in '/home/<USER>/robot/robot_ws/build/robot_description' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/robot/robot_ws/build/robot_description
