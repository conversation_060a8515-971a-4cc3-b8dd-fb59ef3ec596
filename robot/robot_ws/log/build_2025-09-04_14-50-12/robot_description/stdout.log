-- The C compiler identification is GNU 11.4.0
-- The CXX compiler identification is GNU 11.4.0
-- Detecting C compiler AB<PERSON> info
-- Detecting C compiler ABI info - done
-- Check for working C compiler: /usr/bin/cc - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler ABI info
-- Detecting CXX compiler ABI info - done
-- Check for working CXX compiler: /usr/bin/c++ - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
-- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter 
-- Found rclcpp: 16.0.12 (/opt/ros/humble/share/rclcpp/cmake)
-- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)
-- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)
-- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
-- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.2")  
-- Found FastRTPS: /opt/ros/humble/include  
-- Using RMW implementation 'rmw_fastrtps_cpp' as default
-- Looking for pthread.h
-- Looking for pthread.h - found
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
-- Found Threads: TRUE  
-- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
-- Added test 'flake8' to check Python code syntax and style conventions
-- Added test 'lint_cmake' to check CMake code style
-- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
-- Added test 'xmllint' to check XML markup files
-- Configuring done
-- Generating done
-- Build files have been written to: /home/<USER>/robot/robot_ws/build/robot_description
-- Install configuration: ""
-- Installing: /home/<USER>/robot/robot_ws/install/robot_description/share/ament_index/resource_index/package_run_dependencies/robot_description
-- Installing: /home/<USER>/robot/robot_ws/install/robot_description/share/ament_index/resource_index/parent_prefix_path/robot_description
-- Installing: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/environment/ament_prefix_path.sh
-- Installing: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/environment/ament_prefix_path.dsv
-- Installing: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/environment/path.sh
-- Installing: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/environment/path.dsv
-- Installing: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/local_setup.bash
-- Installing: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/local_setup.sh
-- Installing: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/local_setup.zsh
-- Installing: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/local_setup.dsv
-- Installing: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/package.dsv
-- Installing: /home/<USER>/robot/robot_ws/install/robot_description/share/ament_index/resource_index/packages/robot_description
-- Installing: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/cmake/robot_descriptionConfig.cmake
-- Installing: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/cmake/robot_descriptionConfig-version.cmake
-- Installing: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/package.xml
-- Installing: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/launch
-- Installing: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/launch/gazebo.launch.py
-- Installing: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/urdf
-- Installing: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/urdf/robot
-- Installing: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/urdf/robot/robot.urdf.xacro
-- Installing: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/urdf/robot/actuator
-- Installing: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/urdf/robot/actuator/wheel.urdf.xacro
-- Installing: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/urdf/robot/base.urdf.xacro
-- Installing: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/urdf/robot/sensor
-- Installing: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/urdf/robot/sensor/imu.urdf.xacro
-- Installing: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/urdf/robot/sensor/laser_front.urdf.xacro
-- Installing: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/urdf/robot/sensor/laser_rear.urdf.xacro
-- Installing: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/urdf/robot/sensor/gazebo_sensor_plugin.xacro
-- Installing: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/config
-- Installing: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/config/ros2_control.yaml
-- Installing: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/world
-- Installing: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/world/custom_room.world
-- Installing: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/world/room
-- Installing: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/world/room/model.sdf
