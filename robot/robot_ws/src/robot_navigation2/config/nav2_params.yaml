# =========================================================================
# === Nav2 Parameters for a Four-Wheeled Ackermann Steering Robot ===
# =========================================================================
# This configuration is optimized for robots with car-like steering kinematics.
# Key changes:
# - AMCL: Motion model set to 'nav2_amcl::OmniMotionModel'.
# - Global Planner: Switched to 'SmacPlanner' for kinematically-aware path planning.
# - Local Controller: Switched to 'RegulatedPurePursuitController' for smooth path tracking.
# - Costmaps: Using a rectangular 'footprint' instead of 'robot_radius'.
# =========================================================================

amcl:
  ros__parameters:
    use_sim_time: True
    # --- 关键修改：为阿克曼机器人选择正确的运动模型 ---
    # 'DifferentialMotionModel' 是错误的，它适用于两轮差速。
    # 对于无法原地旋转的机器人（如阿克曼），'OmniMotionModel' 是一个更通用、更合适的选择。
    # 它不假设机器人能原地旋转，更多地依赖激光雷达匹配。
    robot_model_type: "nav2_amcl::OmniMotionModel"

    # --- 粒子和更新设置 ---
    max_particles: 5000
    min_particles: 1000
    update_min_d: 0.1         # 机器人移动0.1米后更新
    update_min_a: 0.2         # 机器人旋转0.2弧度后更新
    resample_interval: 1      # 每次更新都重采样

    # --- 激光雷达模型参数 ---
    laser_model_type: "likelihood_field"
    laser_max_range: 12.0
    laser_min_range: 0.15
    max_beams: 180            # 使用更多的激光束来提高精度
    z_hit: 0.95
    z_rand: 0.05
    sigma_hit: 0.2
    
    # --- 里程计噪声模型 (这些值需要根据你的真实里程计质量进行调整) ---
    alpha1: 0.05               # 旋转对旋转噪声
    alpha2: 0.05              # 平移对旋转噪声
    alpha3: 0.02               # 平移对平移噪声
    alpha4: 0.02               # 旋转对平移噪声
    
    # --- 其他参数 ---
    base_frame_id: "base_link"
    global_frame_id: "map"
    odom_frame_id: "odom"
    transform_tolerance: 0.5  # 增加容忍度以应对TF延迟
    recovery_alpha_slow: 0.0  # 禁用恢复，依赖行为树
    recovery_alpha_fast: 0.0
    initial_pose:
      x: 0.0
      y: 0.0
      z: 0.0
      yaw: 0.0
    always_reset_initial_pose: false

bt_navigator:
  ros__parameters:
    use_sim_time: True
    global_frame: map
    robot_base_frame: base_link
    odom_topic: /odom
    bt_loop_duration: 10
    default_server_timeout: 20
    # 使用默认的带重规划和恢复的行为树
    # nav2_bt_navigator/navigate_to_pose_w_replanning_and_recovery.xml
    plugin_lib_names:
      - nav2_compute_path_to_pose_action_bt_node
      - nav2_compute_path_through_poses_action_bt_node
      - nav2_smooth_path_action_bt_node
      - nav2_follow_path_action_bt_node
      - nav2_spin_action_bt_node
      - nav2_wait_action_bt_node
      - nav2_assisted_teleop_action_bt_node
      - nav2_back_up_action_bt_node
      - nav2_drive_on_heading_bt_node
      - nav2_clear_costmap_service_bt_node
      - nav2_is_stuck_condition_bt_node
      - nav2_goal_reached_condition_bt_node
      - nav2_goal_updated_condition_bt_node
      - nav2_globally_updated_goal_condition_bt_node
      - nav2_is_path_valid_condition_bt_node
      - nav2_initial_pose_received_condition_bt_node
      - nav2_reinitialize_global_localization_service_bt_node
      - nav2_rate_controller_bt_node
      - nav2_distance_controller_bt_node
      - nav2_speed_controller_bt_node
      - nav2_truncate_path_action_bt_node
      - nav2_truncate_path_local_action_bt_node
      - nav2_goal_updater_node_bt_node
      - nav2_recovery_node_bt_node
      - nav2_pipeline_sequence_bt_node
      - nav2_round_robin_node_bt_node
      - nav2_transform_available_condition_bt_node
      - nav2_time_expired_condition_bt_node
      - nav2_path_expiring_timer_condition
      - nav2_distance_traveled_condition_bt_node
      - nav2_single_trigger_bt_node
      - nav2_goal_updated_controller_bt_node
      - nav2_is_battery_low_condition_bt_node
      - nav2_navigate_through_poses_action_bt_node
      - nav2_navigate_to_pose_action_bt_node
      - nav2_remove_passed_goals_action_bt_node
      - nav2_planner_selector_bt_node
      - nav2_controller_selector_bt_node
      - nav2_goal_checker_selector_bt_node
      - nav2_controller_cancel_bt_node
      - nav2_path_longer_on_approach_bt_node
      - nav2_wait_cancel_bt_node
      - nav2_spin_cancel_bt_node
      - nav2_back_up_cancel_bt_node
      - nav2_assisted_teleop_cancel_bt_node
      - nav2_drive_on_heading_cancel_bt_node
      - nav2_is_battery_charging_condition_bt_node

controller_server:
  ros__parameters:
    use_sim_time: True
    controller_frequency: 20.0
    min_x_velocity_threshold: 0.001
    min_y_velocity_threshold: 0.5
    min_theta_velocity_threshold: 0.001
    failure_tolerance: 0.3
    progress_checker_plugin: "progress_checker"
    goal_checker_plugins: ["goal_checker"] # Renamed for clarity
    
    # --- 关键修改：将控制器插件替换为 Regulated Pure Pursuit ---
    controller_plugins: ["FollowPath"]

    # 进度检查器参数
    progress_checker:
      plugin: "nav2_controller::SimpleProgressChecker"
      required_movement_radius: 0.5
      movement_time_allowance: 10.0
      
    # 目标检查器参数
    goal_checker:
      plugin: "nav2_controller::SimpleGoalChecker"
      xy_goal_tolerance: 0.25     # 到达目标的 xy 容忍度
      yaw_goal_tolerance: 0.25    # 到达目标的 yaw 容忍度
      stateful: True

    # --- Regulated Pure Pursuit (RPP) 控制器配置 ---
    FollowPath:
      plugin: "nav2_regulated_pure_pursuit_controller::RegulatedPurePursuitController"
      lookahead_dist: 0.6         # 前视距离，关键调整参数
      min_lookahead_dist: 0.3
      max_lookahead_dist: 0.9
      lookahead_time: 1.5
      use_velocity_scaled_lookahead_dist: false
      min_approach_linear_velocity: 0.1
      max_allowed_time_to_collision_up_to_goal: 1.0
      use_regulated_linear_velocity_scaling: true
      use_cost_regulated_linear_velocity_scaling: false
      regulated_linear_scaling_min_radius: 0.9
      regulated_linear_scaling_min_speed: 0.25
      allow_reversing: true      
      use_approach_vel_scaling: true
      min_approach_linear_velocity: 0.05
      max_robot_pose_search_dist: 10.0
      
      # 速度限制
      speed_limit_topic: "/speed_limit"
      max_speed: 1.0
      min_speed: 0.0
      
      # 角度到速度的转换
      use_rotate_to_heading: false
      #rotate_to_heading_min_angle: 0.785
      #rotate_to_heading_angular_vel: 1.8

# --- 关键修改：将 planner_server 放在 controller_server 之后，结构更清晰 ---
planner_server:
  ros__parameters:
    expected_planner_frequency: 20.0
    use_sim_time: True
    
    # --- 关键修改：将全局规划器替换为 SmacPlanner ---
    planner_plugins: ["GridBased"]

    # GridBased (Hybrid-A*) 配置
    GridBased:
      plugin: "nav2_smac_planner/SmacPlannerHybrid"
      tolerance: 0.5
      downsample_costmap: false
      
      # --- 阿克曼机器人的核心参数 ---
      min_turning_radius: 0.4                 # 【【【重要】】】请替换为你的机器人的实际最小转弯半径
      motion_model_for_search: "REEDS_SHEPP"  # 允许前进和后退规划，更灵活
      cost_travel_multiplier: 2.0
      allow_unknown: true

local_costmap:
  local_costmap:
    ros__parameters:
      update_frequency: 5.0
      publish_frequency: 2.0
      global_frame: odom
      robot_base_frame: base_link
      use_sim_time: True
      rolling_window: true
      width: 3
      height: 3
      resolution: 0.05
      
      # --- 关键修改：使用精确的矩形 footprint ---
      # 【【【重要】】】请根据你的机器人实际尺寸修改这四个点的坐标
      # 格式: [[前右], [后右], [后左], [前左]]
      footprint: "[[0.25, 0.2], [0.25, -0.2], [-0.25, -0.2], [-0.25, 0.2]]"
      # robot_radius: 0.22  # 已被 footprint 替代，注释掉或删除
      
      plugins: ["voxel_layer", "inflation_layer"]
      
      voxel_layer:
        plugin: "nav2_costmap_2d::VoxelLayer"
        enabled: True
        publish_voxel_map: True
        origin_z: 0.0
        z_resolution: 0.05
        z_voxels: 16
        max_obstacle_height: 2.0
        mark_threshold: 0
        observation_sources: scan
        scan:
          topic: /scan
          max_obstacle_height: 2.0
          clearing: True
          marking: True
          data_type: "LaserScan"

      inflation_layer:
        plugin: "nav2_costmap_2d::InflationLayer"
        cost_scaling_factor: 3.0
        inflation_radius: 0.55    # 膨胀半径，确保机器人能通过狭窄空间

      always_send_full_costmap: True

global_costmap:
  global_costmap:
    ros__parameters:
      update_frequency: 1.0
      publish_frequency: 1.0
      global_frame: map
      robot_base_frame: base_link
      use_sim_time: True
      resolution: 0.05
      track_unknown_space: true
      
      # --- 关键修改：使用与 local_costmap 相同的 footprint ---
      footprint: "[[0.25, 0.2], [0.25, -0.2], [-0.25, -0.2], [-0.25, 0.2]]"
      # robot_radius: 0.22 # 已被 footprint 替代，注释掉或删除

      plugins: ["static_layer", "obstacle_layer", "inflation_layer"]
      
      static_layer:
        plugin: "nav2_costmap_2d::StaticLayer"
        map_subscribe_transient_local: True
      
      obstacle_layer:
        plugin: "nav2_costmap_2d::ObstacleLayer"
        enabled: True
        observation_sources: scan
        scan:
          topic: /scan
          max_obstacle_height: 2.0
          clearing: True
          marking: True
          data_type: "LaserScan"

      inflation_layer:
        plugin: "nav2_costmap_2d::InflationLayer"
        cost_scaling_factor: 3.0
        inflation_radius: 0.55
      
      always_send_full_costmap: True

# 其他服务器配置保持不变
map_server:
  ros__parameters:
    use_sim_time: True
    yaml_filename: "room.yaml" # 【重要】确保这里指向你的地图文件，或在launch中设置

map_saver:
  ros__parameters:
    use_sim_time: True
    save_map_timeout: 5.0
    free_thresh_default: 0.25
    occupied_thresh_default: 0.65
    map_subscribe_transient_local: True

smoother_server:
  ros__parameters:
    use_sim_time: True
    smoother_plugins: ["simple_smoother"]
    simple_smoother:
      plugin: "nav2_smoother::SimpleSmoother"
      tolerance: 1.0e-10
      max_its: 1000
      do_refinement: True

behavior_server:
  ros__parameters:
    use_sim_time: True
    costmap_topic: local_costmap/costmap_raw
    footprint_topic: local_costmap/published_footprint
    cycle_frequency: 10.0
    behavior_plugins: ["spin", "backup", "drive_on_heading", "wait"]
    spin:
      plugin: "nav2_behaviors/Spin"
    backup:
      plugin: "nav2_behaviors/BackUp"
    drive_on_heading:
      plugin: "nav2_behaviors/DriveOnHeading"
    wait:
      plugin: "nav2_behaviors/Wait"
    global_frame: odom # 注意：行为服务器通常在odom框架下操作
    robot_base_frame: base_link
    transform_tolerance: 0.1
    simulate_ahead_time: 2.0
    max_rotational_vel: 1.0
    min_rotational_vel: 0.4
    rotational_acc_lim: 3.2

waypoint_follower:
  ros__parameters:
    use_sim_time: True
    loop_rate: 20
    stop_on_failure: false
    waypoint_task_executor_plugin: "wait_at_waypoint"
    wait_at_waypoint:
      plugin: "nav2_waypoint_follower::WaitAtWaypoint"
      enabled: True
      waypoint_pause_duration: 200

velocity_smoother:
  ros__parameters:
    use_sim_time: True
    smoothing_frequency: 20.0
    scale_velocities: false
    feedback: "OPEN_LOOP"
    max_velocity: [1.0, 0.0, 1.8] # 对应 x, y, theta 的最大速度
    min_velocity: [-1.0, 0.0, -1.8]
    max_accel: [2.5, 0.0, 3.2]
    max_decel: [-2.5, 0.0, -3.2]
    odom_topic: "odom"
    odom_duration: 0.1
    deadband_velocity: [0.0, 0.0, 0.0]
    velocity_timeout: 1.0