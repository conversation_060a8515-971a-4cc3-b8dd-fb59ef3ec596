# 四轮差速机器人局部导航配置

本配置专门为四轮差速驱动机器人设计，实现基于雷达和IMU的局部动态导航，无需构建SLAM全局地图。

## 主要特性

- **无需全局地图**: 不依赖SLAM构建的全局地图
- **实时障碍物检测**: 基于激光雷达实时检测和避障
- **差速驱动优化**: 专门为四轮差速机器人优化的控制器
- **局部路径规划**: 在局部代价地图上进行路径规划
- **动态避障**: 实时响应环境变化

## 系统架构

### 核心组件
1. **Controller Server**: 使用DWB控制器，专为差速驱动设计
2. **Planner Server**: 使用NavFn规划器进行局部路径规划
3. **Behavior Server**: 处理恢复行为（旋转、后退等）
4. **BT Navigator**: 行为树导航器协调各组件
5. **Velocity Smoother**: 速度平滑器确保安全的速度变化

### 禁用组件
- **AMCL**: 无需全局定位
- **Map Server**: 无需静态地图
- **Global Costmap**: 仅使用局部代价地图

## 配置文件说明

### nav2_params.yaml 主要修改

1. **控制器配置**:
   - 使用 `dwb_core::DWBLocalPlanner`
   - 针对差速驱动的速度限制和加速度限制
   - 优化的轨迹评估参数

2. **局部代价地图**:
   - 更大的窗口尺寸 (6x6m)
   - 更高的更新频率 (10Hz)
   - 基于机器人实际尺寸的footprint

3. **行为树导航器**:
   - 全局坐标系改为 `odom`
   - 适合局部导航的行为树配置

## 使用方法

### 1. 启动局部导航

```bash
# 启动机器人描述和控制器
ros2 launch robot_description robot.launch.py

# 启动局部导航系统
ros2 launch robot_navigation2 local_navigation.launch.py
```

### 2. 发送导航目标

通过RViz2发送导航目标：
1. 在RViz中点击 "2D Nav Goal" 工具
2. 在地图上点击并拖拽设置目标位置和方向

或通过命令行：
```bash
ros2 topic pub /goal_pose geometry_msgs/PoseStamped "
header:
  frame_id: 'odom'
pose:
  position:
    x: 2.0
    y: 1.0
    z: 0.0
  orientation:
    x: 0.0
    y: 0.0
    z: 0.0
    w: 1.0"
```

### 3. 测试局部导航

运行测试脚本：
```bash
cd robot/robot_ws/src/robot_navigation2/scripts
chmod +x test_local_nav.py
ros2 run python3 test_local_nav.py
```

## 重要参数调整

### 机器人尺寸参数
根据你的机器人实际尺寸调整footprint：
```yaml
footprint: "[[0.3, 0.2], [-0.3, 0.2], [-0.3, -0.2], [0.3, -0.2]]"
```

### 速度限制
根据机器人性能调整：
```yaml
max_vel_x: 1.0      # 最大线速度
max_vel_theta: 1.8  # 最大角速度
acc_lim_x: 2.5      # 线加速度限制
acc_lim_theta: 3.2  # 角加速度限制
```

### 安全距离
调整膨胀半径确保安全：
```yaml
inflation_radius: 0.6  # 障碍物膨胀半径
```

## 故障排除

### 1. 机器人不移动
- 检查 `/cmd_vel` 话题是否有数据
- 确认ros2_control配置正确
- 验证TF树完整性

### 2. 路径规划失败
- 增大局部代价地图尺寸
- 调整膨胀半径
- 检查激光雷达数据

### 3. 频繁震荡
- 降低控制器频率
- 调整DWB控制器参数
- 增加速度平滑器参数

## 监控话题

重要话题监控：
```bash
# 查看局部代价地图
ros2 topic echo /local_costmap/costmap

# 查看规划路径
ros2 topic echo /plan

# 查看控制命令
ros2 topic echo /cmd_vel

# 查看机器人状态
ros2 topic echo /odom
```

## 性能优化建议

1. **传感器配置**:
   - 确保激光雷达覆盖360度
   - IMU提供准确的角速度数据
   - 里程计提供可靠的位置估计

2. **计算资源**:
   - 控制器频率不要设置过高
   - 适当调整代价地图分辨率
   - 合理设置轨迹采样数量

3. **环境适应**:
   - 根据工作环境调整安全距离
   - 针对地面条件调整速度限制
   - 考虑动态障碍物的响应时间
