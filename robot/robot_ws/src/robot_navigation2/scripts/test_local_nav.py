#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from geometry_msgs.msg import Po<PERSON>Stamped, Twist
from nav_msgs.msg import Odometry
import math
import time

class LocalNavTester(Node):
    def __init__(self):
        super().__init__('local_nav_tester')
        
        # Publishers
        self.goal_pub = self.create_publisher(PoseStamped, '/goal_pose', 10)
        self.cmd_vel_pub = self.create_publisher(Twist, '/diff_drive_controller/cmd_vel_unstamped', 10)
        
        # Subscribers
        self.odom_sub = self.create_subscription(Odometry, '/odom', self.odom_callback, 10)
        
        # Current robot pose
        self.current_x = 0.0
        self.current_y = 0.0
        self.current_yaw = 0.0
        
        self.get_logger().info('Local Navigation Tester initialized')
        
        # Wait a bit for everything to initialize
        time.sleep(2.0)
        
        # Start testing
        self.test_local_navigation()
    
    def odom_callback(self, msg):
        """Update current robot pose from odometry"""
        self.current_x = msg.pose.pose.position.x
        self.current_y = msg.pose.pose.position.y
        
        # Convert quaternion to yaw
        orientation = msg.pose.pose.orientation
        siny_cosp = 2 * (orientation.w * orientation.z + orientation.x * orientation.y)
        cosy_cosp = 1 - 2 * (orientation.y * orientation.y + orientation.z * orientation.z)
        self.current_yaw = math.atan2(siny_cosp, cosy_cosp)
    
    def send_goal(self, x, y, yaw=0.0):
        """Send a navigation goal"""
        goal = PoseStamped()
        goal.header.frame_id = 'odom'
        goal.header.stamp = self.get_clock().now().to_msg()
        
        goal.pose.position.x = x
        goal.pose.position.y = y
        goal.pose.position.z = 0.0
        
        # Convert yaw to quaternion
        goal.pose.orientation.x = 0.0
        goal.pose.orientation.y = 0.0
        goal.pose.orientation.z = math.sin(yaw / 2.0)
        goal.pose.orientation.w = math.cos(yaw / 2.0)
        
        self.goal_pub.publish(goal)
        self.get_logger().info(f'Sent goal: x={x:.2f}, y={y:.2f}, yaw={yaw:.2f}')
    
    def test_basic_movement(self):
        """Test basic movement commands"""
        self.get_logger().info('Testing basic movement...')
        
        # Move forward
        cmd = Twist()
        cmd.linear.x = 0.5
        cmd.angular.z = 0.0
        
        for i in range(20):  # 2 seconds at 10Hz
            self.cmd_vel_pub.publish(cmd)
            time.sleep(0.1)
        
        # Stop
        cmd.linear.x = 0.0
        self.cmd_vel_pub.publish(cmd)
        time.sleep(1.0)
        
        # Turn in place
        cmd.linear.x = 0.0
        cmd.angular.z = 0.5
        
        for i in range(20):  # 2 seconds at 10Hz
            self.cmd_vel_pub.publish(cmd)
            time.sleep(0.1)
        
        # Stop
        cmd.angular.z = 0.0
        self.cmd_vel_pub.publish(cmd)
        
        self.get_logger().info('Basic movement test completed')
    
    def test_local_navigation(self):
        """Test local navigation with goals"""
        self.get_logger().info('Starting local navigation test...')
        
        # Test 1: Basic movement
        self.test_basic_movement()
        time.sleep(2.0)
        
        # Test 2: Send navigation goals
        goals = [
            (2.0, 0.0, 0.0),      # Forward 2m
            (2.0, 2.0, math.pi/2), # Right 2m, face north
            (0.0, 2.0, math.pi),   # Left 2m, face west
            (0.0, 0.0, 0.0)        # Return to origin, face east
        ]
        
        for i, (x, y, yaw) in enumerate(goals):
            self.get_logger().info(f'Sending goal {i+1}/4: ({x}, {y}, {yaw:.2f})')
            self.send_goal(x, y, yaw)
            
            # Wait for goal to be processed
            time.sleep(10.0)
            
            # Check if we're close to the goal
            distance = math.sqrt((self.current_x - x)**2 + (self.current_y - y)**2)
            self.get_logger().info(f'Current position: ({self.current_x:.2f}, {self.current_y:.2f})')
            self.get_logger().info(f'Distance to goal: {distance:.2f}m')
        
        self.get_logger().info('Local navigation test completed!')

def main(args=None):
    rclpy.init(args=args)
    
    tester = LocalNavTester()
    
    try:
        rclpy.spin(tester)
    except KeyboardInterrupt:
        pass
    finally:
        tester.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
