#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from geometry_msgs.msg import PoseStamped, Twist
from nav_msgs.msg import Odometry
from sensor_msgs.msg import LaserScan
import math
import numpy as np

class DirectGoalNavigator(Node):
    def __init__(self):
        super().__init__('direct_goal_navigator')
        
        # Publishers
        self.cmd_vel_pub = self.create_publisher(Twist, '/diff_drive_controller/cmd_vel_unstamped', 10)
        
        # Subscribers
        self.odom_sub = self.create_subscription(Odometry, '/odom', self.odom_callback, 10)
        self.scan_sub = self.create_subscription(LaserScan, '/scan', self.scan_callback, 10)
        self.goal_sub = self.create_subscription(PoseStamped, '/goal_pose', self.goal_callback, 10)
        
        # Robot state
        self.current_x = 0.0
        self.current_y = 0.0
        self.current_yaw = 0.0
        
        # Goal state
        self.goal_x = None
        self.goal_y = None
        self.goal_yaw = None
        self.has_goal = False
        
        # Laser scan data
        self.laser_ranges = []
        self.laser_angle_min = 0.0
        self.laser_angle_increment = 0.0
        
        # Control parameters
        self.max_linear_vel = 0.8
        self.max_angular_vel = 1.5
        self.goal_tolerance = 0.3
        self.angle_tolerance = 0.2
        self.obstacle_distance = 1.0  # Stop if obstacle closer than this
        
        # Control timer
        self.control_timer = self.create_timer(0.1, self.control_loop)  # 10Hz
        
        self.get_logger().info('Direct Goal Navigator initialized')
    
    def odom_callback(self, msg):
        """Update current robot pose from odometry"""
        self.current_x = msg.pose.pose.position.x
        self.current_y = msg.pose.pose.position.y
        
        # Convert quaternion to yaw
        orientation = msg.pose.pose.orientation
        siny_cosp = 2 * (orientation.w * orientation.z + orientation.x * orientation.y)
        cosy_cosp = 1 - 2 * (orientation.y * orientation.y + orientation.z * orientation.z)
        self.current_yaw = math.atan2(siny_cosp, cosy_cosp)
    
    def scan_callback(self, msg):
        """Update laser scan data"""
        self.laser_ranges = msg.ranges
        self.laser_angle_min = msg.angle_min
        self.laser_angle_increment = msg.angle_increment
    
    def goal_callback(self, msg):
        """Receive new goal"""
        if msg.header.frame_id == 'odom':
            self.goal_x = msg.pose.position.x
            self.goal_y = msg.pose.position.y
            
            # Convert quaternion to yaw
            orientation = msg.pose.orientation
            siny_cosp = 2 * (orientation.w * orientation.z + orientation.x * orientation.y)
            cosy_cosp = 1 - 2 * (orientation.y * orientation.y + orientation.z * orientation.z)
            self.goal_yaw = math.atan2(siny_cosp, cosy_cosp)
            
            self.has_goal = True
            self.get_logger().info(f'New goal received: ({self.goal_x:.2f}, {self.goal_y:.2f}, {self.goal_yaw:.2f})')
        else:
            self.get_logger().warn(f'Goal in frame {msg.header.frame_id} not supported, use odom frame')
    
    def check_obstacles(self):
        """Check for obstacles in front of the robot"""
        if not self.laser_ranges:
            return False
        
        # Check front 60 degrees (-30 to +30 degrees)
        front_angle_range = math.pi / 6  # 30 degrees
        num_ranges = len(self.laser_ranges)
        
        for i, range_val in enumerate(self.laser_ranges):
            if math.isnan(range_val) or math.isinf(range_val):
                continue
                
            angle = self.laser_angle_min + i * self.laser_angle_increment
            
            # Check if this reading is in the front sector
            if abs(angle) <= front_angle_range:
                if range_val < self.obstacle_distance:
                    return True
        
        return False
    
    def control_loop(self):
        """Main control loop"""
        if not self.has_goal:
            return
        
        # Calculate distance and angle to goal
        dx = self.goal_x - self.current_x
        dy = self.goal_y - self.current_y
        distance_to_goal = math.sqrt(dx*dx + dy*dy)
        angle_to_goal = math.atan2(dy, dx)
        
        # Check if goal is reached
        if distance_to_goal < self.goal_tolerance:
            # Check orientation
            angle_diff = self.normalize_angle(self.goal_yaw - self.current_yaw)
            if abs(angle_diff) < self.angle_tolerance:
                self.get_logger().info('Goal reached!')
                self.has_goal = False
                self.stop_robot()
                return
            else:
                # Only rotate to final orientation
                self.rotate_to_angle(self.goal_yaw)
                return
        
        # Calculate angle difference
        angle_diff = self.normalize_angle(angle_to_goal - self.current_yaw)
        
        # Create velocity command
        cmd = Twist()
        
        # Check for obstacles
        if self.check_obstacles():
            self.get_logger().warn('Obstacle detected! Stopping.')
            self.stop_robot()
            return
        
        # Simple proportional controller
        if abs(angle_diff) > 0.3:  # Need to turn first
            cmd.linear.x = 0.0
            cmd.angular.z = self.max_angular_vel * (1.0 if angle_diff > 0 else -1.0)
        else:
            # Move forward while adjusting heading
            linear_gain = min(1.0, distance_to_goal / 2.0)  # Slow down near goal
            cmd.linear.x = self.max_linear_vel * linear_gain
            
            # Proportional angular control
            angular_gain = 2.0
            cmd.angular.z = max(-self.max_angular_vel, 
                              min(self.max_angular_vel, angular_gain * angle_diff))
        
        # Ensure minimum linear velocity when turning (differential drive requirement)
        if abs(cmd.angular.z) > 0.1 and abs(cmd.linear.x) < 0.1:
            cmd.linear.x = 0.1  # Minimum forward velocity during turns
        
        self.cmd_vel_pub.publish(cmd)
    
    def rotate_to_angle(self, target_yaw):
        """Rotate to specific angle"""
        angle_diff = self.normalize_angle(target_yaw - self.current_yaw)
        
        cmd = Twist()
        if abs(angle_diff) > self.angle_tolerance:
            cmd.linear.x = 0.1  # Small forward velocity for differential drive
            cmd.angular.z = self.max_angular_vel * 0.5 * (1.0 if angle_diff > 0 else -1.0)
        else:
            cmd.linear.x = 0.0
            cmd.angular.z = 0.0
        
        self.cmd_vel_pub.publish(cmd)
    
    def stop_robot(self):
        """Stop the robot"""
        cmd = Twist()
        cmd.linear.x = 0.0
        cmd.angular.z = 0.0
        self.cmd_vel_pub.publish(cmd)
    
    def normalize_angle(self, angle):
        """Normalize angle to [-pi, pi]"""
        while angle > math.pi:
            angle -= 2 * math.pi
        while angle < -math.pi:
            angle += 2 * math.pi
        return angle

def main(args=None):
    rclpy.init(args=args)
    
    navigator = DirectGoalNavigator()
    
    try:
        rclpy.spin(navigator)
    except KeyboardInterrupt:
        pass
    finally:
        navigator.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
