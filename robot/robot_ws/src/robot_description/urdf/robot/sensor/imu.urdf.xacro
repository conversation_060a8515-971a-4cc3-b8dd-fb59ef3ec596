<?xml version="1.0"?>
<robot xmlns:xacro="http://www.ros.org/wiki/xacro">
    <link name="imu_link">
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0" /> <!-- Centered position -->
            <geometry>
                <box size="0.02 0.02 0.02" />
            </geometry>
            <material name="black">
                <color rgba="0 0 0 0.8" />
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 0" rpy="0 0 0" /> <!-- Centered position -->
            <geometry>
                <box size="0.02 0.02 0.02" />
            </geometry>
        </collision>
        <inertial>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <mass value="0.01" />
            <inertia ixx="0.000007" ixy="0" ixz="0" iyy="0.000007" iyz="0" izz="0.000007" />
        </inertial>
    </link>

    <joint name="imu_joint" type="fixed">
        <parent link="base_link" />
        <child link="imu_link" />
        <origin xyz="0 0 0" rpy="0 0 0" /> <!-- 车身中心内部 -->
    </joint>
</robot>