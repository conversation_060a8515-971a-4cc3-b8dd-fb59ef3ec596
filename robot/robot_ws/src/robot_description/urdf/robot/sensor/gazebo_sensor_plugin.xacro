<?xml version="1.0"?>
<robot xmlns:xacro="http://www.ros.org/wiki/xacro">
        <gazebo reference="laser_front_link">
            <sensor name="laserscan_front" type="ray">
                <plugin name="laserscanf" filename="libgazebo_ros_ray_sensor.so">
                    <ros>
                        <namespace>/</namespace>
                        <remapping>~/out:=scan_front</remapping>
                        <use_sim_time>true</use_sim_time>
                    </ros>
                    <!-- 发布为 PointCloud2 -->
                    <output_type>sensor_msgs/PointCloud2</output_type>
                    <frame_name>laser_front_link</frame_name>
                </plugin>
                <always_on>true</always_on>
                <visualize>true</visualize>
                <update_rate>50</update_rate>
                <pose>0 0 0 0 0 0</pose>
							<!-- 激光传感器配置：360° 平面扫描 -->
                <ray>
                    <scan>
                        <horizontal>
                            <!-- 1080 采样 ≈ 0.333°/点；可按需调整为 720 或 1440 -->
                            <samples>1080</samples>
                            <resolution>1.0</resolution>
                            <min_angle>-3.14159</min_angle>
                            <max_angle>3.14159</max_angle>
                        </horizontal>
                    </scan>
                    <range>
                        <min>0.12</min>
                        <max>10.0</max>
                        <resolution>0.015</resolution>
                    </range>
                    <noise>
                        <type>gaussian</type>
                        <mean>0.0</mean>
                        <stddev>0.01</stddev>
                    </noise>
                </ray>
            </sensor>
        </gazebo>

        <gazebo reference="laser_rear_link">
            <sensor name="laserscan_rear" type="ray">
                <plugin name="laserscanr" filename="libgazebo_ros_ray_sensor.so">
                    <ros>
                        <namespace>/</namespace>
                        <remapping>~/out:=scan_rear</remapping>
                        <use_sim_time>true</use_sim_time>
                    </ros>
                    <!-- 发布为 PointCloud2 -->
                    <output_type>sensor_msgs/PointCloud2</output_type>
                    <frame_name>laser_rear_link</frame_name>
                </plugin>
                <always_on>true</always_on>
                <visualize>true</visualize>
                <update_rate>50</update_rate>
                <pose>0 0 0 0 0 0</pose>
							<!-- 激光传感器配置：360° 平面扫描 -->
                <ray>
                    <scan>
                        <horizontal>
                            <samples>1080</samples>
                            <resolution>1.0</resolution>
                            <min_angle>-3.14159</min_angle>
                            <max_angle>3.14159</max_angle>
                        </horizontal>
                    </scan>
                    <range>
                        <min>0.12</min>
                        <max>10.0</max>
                        <resolution>0.015</resolution>
                    </range>
                    <noise>
                        <type>gaussian</type>
                        <mean>0.0</mean>
                        <stddev>0.01</stddev>
                    </noise>
                </ray>
            </sensor>
        </gazebo>

<gazebo reference="imu_link">
    <sensor name="imu_sensor" type="imu">
        <plugin name="imu_plugin" filename="libgazebo_ros_imu_sensor.so">
            <ros>
                <namespace>/</namespace>
                <remapping>~/out:=imu</remapping>
                <use_sim_time>true</use_sim_time>
            </ros>
            <frame_name>base_link</frame_name>
            <initial_orientation_as_reference>false</initial_orientation_as_reference>
        </plugin>
        <update_rate>100</update_rate>
        <always_on>true</always_on>
        <!-- 六轴噪声设置 -->
        <imu>
            <angular_velocity>
                <x>
                    <noise type="gaussian">
                        <mean>0.0</mean>
                        <stddev>2e-4</stddev>
                        <bias_mean>0.0000075</bias_mean>
                        <bias_stddev>0.0000008</bias_stddev>
                    </noise>
                </x>
                <y>
                    <noise type="gaussian">
                        <mean>0.0</mean>
                        <stddev>2e-4</stddev>
                        <bias_mean>0.0000075</bias_mean>
                        <bias_stddev>0.0000008</bias_stddev>
                    </noise>
                </y>
                <z>
                    <noise type="gaussian">
                        <mean>0.0</mean>
                        <stddev>2e-4</stddev>
                        <bias_mean>0.0000075</bias_mean>
                        <bias_stddev>0.0000008</bias_stddev>
                    </noise>
                </z>
            </angular_velocity>
            <linear_acceleration>
                <x>
                    <noise type="gaussian">
                        <mean>0.0</mean>
                        <stddev>1.7e-2</stddev>
                        <bias_mean>0.1</bias_mean>
                        <bias_stddev>0.001</bias_stddev>
                    </noise>
                </x>
                <y>
                    <noise type="gaussian">
                        <mean>0.0</mean>
                        <stddev>1.7e-2</stddev>
                        <bias_mean>0.1</bias_mean>
                        <bias_stddev>0.001</bias_stddev>
                    </noise>
                </y>
                <z>
                    <noise type="gaussian">
                        <mean>0.0</mean>
                        <stddev>1.7e-2</stddev>
                        <bias_mean>0.1</bias_mean>
                        <bias_stddev>0.001</bias_stddev>
                    </noise>
                </z>
            </linear_acceleration>
        </imu>
    </sensor>
</gazebo>

</robot>