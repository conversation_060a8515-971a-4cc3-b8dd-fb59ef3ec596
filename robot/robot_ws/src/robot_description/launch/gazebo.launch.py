import os
import launch
import launch_ros
from launch.actions import IncludeLaunchDescription, RegisterEventHandler
from launch.event_handlers import OnProcessExit
from launch.launch_description_sources import PythonLaunchDescriptionSource
from launch_ros.actions import Node
from ament_index_python.packages import get_package_share_directory
from launch.substitutions import Command, LaunchConfiguration

def generate_launch_description():
    robot_name = "robot"

    # 路径
    pkg_path = get_package_share_directory('robot_description')
    default_model_path = os.path.join(pkg_path, 'urdf', 'robot', 'robot.urdf.xacro')
    ros2_control_config_path = os.path.join(pkg_path, 'config', 'ros2_control.yaml')
    default_world_path = os.path.join(pkg_path, 'world', 'custom_room.world')

    # 参数声明
    declare_model_path_cmd = launch.actions.DeclareLaunchArgument(
        name='model',
        default_value=default_model_path,
        description='Absolute path to robot urdf file'
    )

    declare_world_path_cmd = launch.actions.DeclareLaunchArgument(
        name='world',
        default_value=default_world_path,
        description='Absolute path to world file'
    )

    # 生成 robot_description
    robot_description = launch_ros.parameter_descriptions.ParameterValue(
        Command([
            'xacro', ' ',
            LaunchConfiguration('model'), ' ',
            'controllers_yaml_path:=', ros2_control_config_path
        ]),
        value_type=str
    )

    # robot_state_publisher
    robot_state_publisher_node = Node(
        package='robot_state_publisher',
        executable='robot_state_publisher',
        parameters=[{'robot_description': robot_description, 'use_sim_time': True}],
        output='screen'
    )

    # Gazebo
    launch_gazebo = IncludeLaunchDescription(
        PythonLaunchDescriptionSource(
            os.path.join(get_package_share_directory('gazebo_ros'), 'launch', 'gazebo.launch.py')
        ),
        launch_arguments={'world': LaunchConfiguration('world')}.items()
    )

    # spawn_entity.py
    spawn_entity_node = Node(
        package='gazebo_ros',
        executable='spawn_entity.py',
        arguments=['-entity', robot_name,
                   '-topic', 'robot_description',
                   '-x', '0.0',
                   '-y', '0.0',
                   '-z', '0.1'],
        output='screen'
    )

    # 关节状态发布器 Spawner
    joint_state_broadcaster_spawner = Node(
        package="controller_manager",
        executable="spawner",
        arguments=["joint_state_broadcaster", "--controller-manager", "/controller_manager"],
        output="screen",
    )
    
    # 差速驱动控制器 Spawner
    diff_drive_spawner = Node(
        package="controller_manager",
        executable="spawner",
        arguments=["diff_drive_controller", "--controller-manager", "/controller_manager"],
        output="screen",
    )

    # ==================== 确保时序正确的事件处理器 ====================
    # 这个事件处理器确保在 spawn_entity_node 成功完成后再启动控制器
    # 这样可以保证Gazebo插件已经加载完毕
    delay_controller_spawners_after_spawn_entity = RegisterEventHandler(
        event_handler=OnProcessExit(
            target_action=spawn_entity_node,
            on_exit=[joint_state_broadcaster_spawner, diff_drive_spawner],
        )
    )
    # =============================================================

    return launch.LaunchDescription([
        declare_model_path_cmd,
        declare_world_path_cmd,
        robot_state_publisher_node,
        launch_gazebo,
        spawn_entity_node,
        # 我们不再直接启动spawner，而是通过事件处理器来启动它们
        delay_controller_spawners_after_spawn_entity
    ])