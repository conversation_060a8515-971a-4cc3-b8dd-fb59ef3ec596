{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-70de5c747b0461aec3ce.json", "minimumCMakeVersion": {"string": "3.12"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2, 3]}], "name": "", "projects": [{"directoryIndexes": [0], "name": "pointcloud_merger", "targetIndexes": [0, 1, 2, 3]}], "targets": [{"directoryIndex": 0, "id": "pointcloud_merger_node::@6890427a1f51a3e7e1df", "jsonFile": "target-pointcloud_merger_node-39029b62414220a7a8c4.json", "name": "pointcloud_merger_node", "projectIndex": 0}, {"directoryIndex": 0, "id": "pointcloud_merger_uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-pointcloud_merger_uninstall-98304441713980bb9230.json", "name": "pointcloud_merger_uninstall", "projectIndex": 0}, {"directoryIndex": 0, "id": "pointcloud_to_laserscan_node::@6890427a1f51a3e7e1df", "jsonFile": "target-pointcloud_to_laserscan_node-baf102f323a92f12fbaa.json", "name": "pointcloud_to_laserscan_node", "projectIndex": 0}, {"directoryIndex": 0, "id": "uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-uninstall-48677077c260cba2847c.json", "name": "uninstall", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/robot/robot_ws/build/pointcloud_merger", "source": "/home/<USER>/robot/robot_ws/src/pointcloud_merger"}, "version": {"major": 2, "minor": 3}}