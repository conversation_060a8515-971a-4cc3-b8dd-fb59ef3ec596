set(_AMENT_PACKAGE_NAME "pointcloud_merger")
set(pointcloud_merger_VERSION "0.0.0")
set(pointcloud_merger_MAINTAINER "fml <<EMAIL>>")
set(pointcloud_merger_BUILD_DEPENDS "rclcpp" "sensor_msgs" "pcl_conversions" "pcl_ros" "laser_geometry" "tf2_ros" "tf2_sensor_msgs" "message_filters" "tf2_eigen")
set(pointcloud_merger_BUILDTOOL_DEPENDS "ament_cmake")
set(pointcloud_merger_BUILD_EXPORT_DEPENDS "rclcpp" "sensor_msgs" "pcl_conversions" "pcl_ros" "laser_geometry" "tf2_ros" "tf2_sensor_msgs" "message_filters" "tf2_eigen")
set(pointcloud_merger_BUILDTOOL_EXPORT_DEPENDS )
set(pointcloud_merger_EXEC_DEPENDS "rclcpp" "sensor_msgs" "pcl_conversions" "pcl_ros" "laser_geometry" "tf2_ros" "tf2_sensor_msgs" "message_filters" "tf2_eigen")
set(pointcloud_merger_TEST_DEPENDS "ament_lint_auto" "ament_lint_common")
set(pointcloud_merger_GROUP_DEPENDS )
set(pointcloud_merger_MEMBER_OF_GROUPS )
set(pointcloud_merger_DEPRECATED "")
set(pointcloud_merger_EXPORT_TAGS)
list(APPEND pointcloud_merger_EXPORT_TAGS "<build_type>ament_cmake</build_type>")
