<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>pointcloud_merger</name>
  <version>0.0.0</version>
  <description>A package for merging pointclouds and converting them into laser scans using ROS 2.</description>
  <maintainer email="<EMAIL>">fml</maintainer>
  <license>TODO: License declaration</license> <!-- Replace 'TODO' with a valid license, e.g., 'Apache-2.0' -->

  <buildtool_depend>ament_cmake</buildtool_depend>

  <depend>rclcpp</depend>
  <depend>sensor_msgs</depend>
  <depend>pcl_conversions</depend>
  <depend>pcl_ros</depend>
  <depend>laser_geometry</depend>
  <depend>tf2_ros</depend>
  <depend>tf2_sensor_msgs</depend>
  <depend>message_filters</depend>
  <depend>tf2_eigen</depend>


  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_lint_common</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
