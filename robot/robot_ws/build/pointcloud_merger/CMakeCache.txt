# This is the CMakeCache file.
# For build in directory: /home/<USER>/robot/robot_ws/build/pointcloud_merger
# It was generated by CMake: /usr/bin/cmake
# You can edit this file to change values found and used by cmake.
# If you do not want to change any of the values, simply exit the editor.
# If you do want to change a value, simply edit, save, and exit the editor.
# The syntax for the file is as follows:
# KEY:TYPE=VALUE
# KEY is the name of a variable in the cache.
# TYPE is a hint to GUIs for the type of VALUE, DO NOT EDIT TYPE!.
# VALUE is the current value for the KEY.

########################
# EXTERNAL cache entries
########################

//Generate environment files in the CMAKE_INSTALL_PREFIX
AMENT_CMAKE_ENVIRONMENT_GENERATION:BOOL=OFF

//Generate environment files in the package share folder
AMENT_CMAKE_ENVIRONMENT_PACKAGE_GENERATION:BOOL=ON

//Generate marker file containing the parent prefix path
AMENT_CMAKE_ENVIRONMENT_PARENT_PREFIX_PATH_GENERATION:BOOL=ON

//Replace the CMake install command with a custom implementation
// using symlinks instead of copying resources
AMENT_CMAKE_SYMLINK_INSTALL:BOOL=OFF

//Generate an uninstall target to revert the effects of the install
// step
AMENT_CMAKE_UNINSTALL_TARGET:BOOL=ON

//The path where test results are generated
AMENT_TEST_RESULTS_DIR:PATH=/home/<USER>/robot/robot_ws/build/pointcloud_merger/test_results

//Build the testing tree.
BUILD_TESTING:BOOL=ON

Boost_DATE_TIME_LIBRARY_RELEASE:STRING=/usr/lib/x86_64-linux-gnu/libboost_date_time.so.1.74.0

//The directory containing a CMake configuration file for Boost.
Boost_DIR:PATH=/usr/lib/x86_64-linux-gnu/cmake/Boost-1.74.0

Boost_FILESYSTEM_LIBRARY_RELEASE:STRING=/usr/lib/x86_64-linux-gnu/libboost_filesystem.so.1.74.0

//Path to a file.
Boost_INCLUDE_DIR:PATH=/usr/include

Boost_IOSTREAMS_LIBRARY_RELEASE:STRING=/usr/lib/x86_64-linux-gnu/libboost_iostreams.so.1.74.0

Boost_SERIALIZATION_LIBRARY_RELEASE:STRING=/usr/lib/x86_64-linux-gnu/libboost_serialization.so.1.74.0

Boost_SYSTEM_LIBRARY_RELEASE:STRING=/usr/lib/x86_64-linux-gnu/libboost_system.so.1.74.0

//Path to a program.
CMAKE_ADDR2LINE:FILEPATH=/usr/bin/addr2line

//Path to a program.
CMAKE_AR:FILEPATH=/usr/bin/ar

//Choose the type of build, options are: None Debug Release RelWithDebInfo
// MinSizeRel ...
CMAKE_BUILD_TYPE:STRING=

//Enable/Disable color output during build.
CMAKE_COLOR_MAKEFILE:BOOL=ON

//CXX compiler
CMAKE_CXX_COMPILER:FILEPATH=/usr/bin/c++

//A wrapper around 'ar' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_CXX_COMPILER_AR:FILEPATH=/usr/bin/gcc-ar-11

//A wrapper around 'ranlib' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_CXX_COMPILER_RANLIB:FILEPATH=/usr/bin/gcc-ranlib-11

//Flags used by the CXX compiler during all build types.
CMAKE_CXX_FLAGS:STRING=

//Flags used by the CXX compiler during DEBUG builds.
CMAKE_CXX_FLAGS_DEBUG:STRING=-g

//Flags used by the CXX compiler during MINSIZEREL builds.
CMAKE_CXX_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the CXX compiler during RELEASE builds.
CMAKE_CXX_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the CXX compiler during RELWITHDEBINFO builds.
CMAKE_CXX_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//C compiler
CMAKE_C_COMPILER:FILEPATH=/usr/bin/cc

//A wrapper around 'ar' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_C_COMPILER_AR:FILEPATH=/usr/bin/gcc-ar-11

//A wrapper around 'ranlib' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_C_COMPILER_RANLIB:FILEPATH=/usr/bin/gcc-ranlib-11

//Flags used by the C compiler during all build types.
CMAKE_C_FLAGS:STRING=

//Flags used by the C compiler during DEBUG builds.
CMAKE_C_FLAGS_DEBUG:STRING=-g

//Flags used by the C compiler during MINSIZEREL builds.
CMAKE_C_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the C compiler during RELEASE builds.
CMAKE_C_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the C compiler during RELWITHDEBINFO builds.
CMAKE_C_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//Path to a program.
CMAKE_DLLTOOL:FILEPATH=CMAKE_DLLTOOL-NOTFOUND

//Flags used by the linker during all build types.
CMAKE_EXE_LINKER_FLAGS:STRING=

//Flags used by the linker during DEBUG builds.
CMAKE_EXE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during MINSIZEREL builds.
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during RELEASE builds.
CMAKE_EXE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during RELWITHDEBINFO builds.
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Enable/Disable output of compile commands during generation.
CMAKE_EXPORT_COMPILE_COMMANDS:BOOL=

//Install path prefix, prepended onto install directories.
CMAKE_INSTALL_PREFIX:PATH=/home/<USER>/robot/robot_ws/install/pointcloud_merger

//Path to a program.
CMAKE_LINKER:FILEPATH=/usr/bin/ld

//Path to a program.
CMAKE_MAKE_PROGRAM:FILEPATH=/usr/bin/gmake

//Flags used by the linker during the creation of modules during
// all build types.
CMAKE_MODULE_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of modules during
// DEBUG builds.
CMAKE_MODULE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of modules during
// MINSIZEREL builds.
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of modules during
// RELEASE builds.
CMAKE_MODULE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of modules during
// RELWITHDEBINFO builds.
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_NM:FILEPATH=/usr/bin/nm

//Path to a program.
CMAKE_OBJCOPY:FILEPATH=/usr/bin/objcopy

//Path to a program.
CMAKE_OBJDUMP:FILEPATH=/usr/bin/objdump

//Value Computed by CMake
CMAKE_PROJECT_DESCRIPTION:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_HOMEPAGE_URL:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_NAME:STATIC=pointcloud_merger

//Path to a program.
CMAKE_RANLIB:FILEPATH=/usr/bin/ranlib

//Path to a program.
CMAKE_READELF:FILEPATH=/usr/bin/readelf

//Flags used by the linker during the creation of shared libraries
// during all build types.
CMAKE_SHARED_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of shared libraries
// during DEBUG builds.
CMAKE_SHARED_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of shared libraries
// during MINSIZEREL builds.
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELEASE builds.
CMAKE_SHARED_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELWITHDEBINFO builds.
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//If set, runtime paths are not added when installing shared libraries,
// but are added when building.
CMAKE_SKIP_INSTALL_RPATH:BOOL=NO

//If set, runtime paths are not added when using shared libraries.
CMAKE_SKIP_RPATH:BOOL=NO

//Flags used by the linker during the creation of static libraries
// during all build types.
CMAKE_STATIC_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of static libraries
// during DEBUG builds.
CMAKE_STATIC_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of static libraries
// during MINSIZEREL builds.
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of static libraries
// during RELEASE builds.
CMAKE_STATIC_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of static libraries
// during RELWITHDEBINFO builds.
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_STRIP:FILEPATH=/usr/bin/strip

//If this value is on, makefiles will be generated without the
// .SILENT directive, and all commands will be echoed to the console
// during the make.  This is useful for debugging only. With Visual
// Studio IDE projects all commands are done without /nologo.
CMAKE_VERBOSE_MAKEFILE:BOOL=FALSE

//Path to a file.
EIGEN_INCLUDE_DIR:PATH=/usr/include/eigen3

//Path to a file.
EXPAT_INCLUDE_DIR:PATH=/usr/include

//Path to a library.
EXPAT_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libexpat.so

//The directory containing a CMake configuration file for Eigen3.
Eigen3_DIR:PATH=/usr/share/eigen3/cmake

//Eigen include directory
Eigen3_INCLUDE_DIR:PATH=/usr/include/eigen3

//Path to a file.
FLANN_INCLUDE_DIR:PATH=/usr/include

//Path to a library.
FLANN_LIBRARY_DEBUG_SHARED:FILEPATH=FLANN_LIBRARY_DEBUG_SHARED-NOTFOUND

//Path to a library.
FLANN_LIBRARY_DEBUG_STATIC:FILEPATH=FLANN_LIBRARY_DEBUG_STATIC-NOTFOUND

//Path to a library.
FLANN_LIBRARY_SHARED:FILEPATH=/usr/lib/x86_64-linux-gnu/libflann_cpp.so

//Path to a library.
FLANN_LIBRARY_STATIC:FILEPATH=/usr/lib/x86_64-linux-gnu/libflann_cpp_s.a

//Path to a file.
FREETYPE_INCLUDE_DIR_freetype2:PATH=/usr/include/freetype2

//Path to a file.
FREETYPE_INCLUDE_DIR_ft2build:PATH=/usr/include/freetype2

//Path to a library.
FREETYPE_LIBRARY_DEBUG:FILEPATH=FREETYPE_LIBRARY_DEBUG-NOTFOUND

//Path to a library.
FREETYPE_LIBRARY_RELEASE:FILEPATH=/usr/lib/x86_64-linux-gnu/libfreetype.so

//Path to a library.
FastCDR_LIBRARY_DEBUG:FILEPATH=FastCDR_LIBRARY_DEBUG-NOTFOUND

//Path to a library.
FastCDR_LIBRARY_RELEASE:FILEPATH=/opt/ros/humble/lib/libfastcdr.so

//Path to a file.
FastRTPS_INCLUDE_DIR:PATH=/opt/ros/humble/include

//Path to a library.
FastRTPS_LIBRARY_DEBUG:FILEPATH=FastRTPS_LIBRARY_DEBUG-NOTFOUND

//Path to a library.
FastRTPS_LIBRARY_RELEASE:FILEPATH=/opt/ros/humble/lib/libfastrtps.so

//Path to a file.
Fontconfig_INCLUDE_DIR:PATH=/usr/include

//Path to a library.
Fontconfig_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libfontconfig.so

//glew include directory
GLEW_INCLUDE_DIR:PATH=/usr/include

//glew library
GLEW_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libGLEW.so

//Path to a file.
JPEG_INCLUDE_DIR:PATH=/usr/include

//Path to a library.
JPEG_LIBRARY_DEBUG:FILEPATH=JPEG_LIBRARY_DEBUG-NOTFOUND

//Path to a library.
JPEG_LIBRARY_RELEASE:FILEPATH=/usr/lib/x86_64-linux-gnu/libjpeg.so

//jsoncpp include directory
JsonCpp_INCLUDE_DIR:PATH=/usr/include/jsoncpp

//jsoncpp library
JsonCpp_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libjsoncpp.so

//lz4 include directory
LZ4_INCLUDE_DIR:PATH=/usr/include

//lz4 library
LZ4_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/liblz4.so

//lzma include directory
LZMA_INCLUDE_DIR:PATH=/usr/include

//lzma library
LZMA_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/liblzma.so

//Executable for running MPI programs.
MPIEXEC_EXECUTABLE:FILEPATH=/usr/bin/mpiexec

//Maximum number of processors available to run MPI applications.
MPIEXEC_MAX_NUMPROCS:STRING=14

//Flag used by MPI to specify the number of processes for mpiexec;
// the next option will be the number of processes.
MPIEXEC_NUMPROC_FLAG:STRING=-n

//These flags will be placed after all flags passed to mpiexec.
MPIEXEC_POSTFLAGS:STRING=

//These flags will be directly before the executable that is being
// run by mpiexec.
MPIEXEC_PREFLAGS:STRING=

//MPI C additional include directories
MPI_C_ADDITIONAL_INCLUDE_DIRS:STRING=/usr/lib/x86_64-linux-gnu/openmpi/include/openmpi

//MPI compiler for C
MPI_C_COMPILER:FILEPATH=/usr/bin/mpicc

//MPI C compiler wrapper include directories
MPI_C_COMPILER_INCLUDE_DIRS:STRING=/usr/lib/x86_64-linux-gnu/openmpi/include;/usr/lib/x86_64-linux-gnu/openmpi/include/openmpi

//MPI C compilation definitions
MPI_C_COMPILE_DEFINITIONS:STRING=

//MPI C compilation options
MPI_C_COMPILE_OPTIONS:STRING=

//Path to a file.
MPI_C_HEADER_DIR:PATH=/usr/lib/x86_64-linux-gnu/openmpi/include

//MPI C libraries to link against
MPI_C_LIB_NAMES:STRING=mpi

//MPI C linker flags
MPI_C_LINK_FLAGS:STRING=-L/usr/lib/x86_64-linux-gnu/openmpi/lib

//Location of the mpi library for MPI
MPI_mpi_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libmpi.so

//Path to a file.
OPENGL_EGL_INCLUDE_DIR:PATH=/usr/include

//Path to a file.
OPENGL_GLES2_INCLUDE_DIR:PATH=/usr/include

//Path to a file.
OPENGL_GLES3_INCLUDE_DIR:PATH=/usr/include

//Path to a file.
OPENGL_GLX_INCLUDE_DIR:PATH=/usr/include

//Path to a file.
OPENGL_INCLUDE_DIR:PATH=/usr/include

//Path to a library.
OPENGL_egl_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libEGL.so

//Path to a library.
OPENGL_gles2_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libGLESv2.so

//Path to a library.
OPENGL_gles3_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libGLESv2.so

//Path to a library.
OPENGL_glu_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libGLU.so

//Path to a library.
OPENGL_glx_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libGLX.so

//Path to a library.
OPENGL_opengl_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libOpenGL.so

//Path to a file.
OPENGL_xmesa_INCLUDE_DIR:PATH=OPENGL_xmesa_INCLUDE_DIR-NOTFOUND

//Path to a file.
OPENNI2_INCLUDE_DIR:PATH=/usr/include/openni2

//Path to a library.
OPENNI2_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libOpenNI2.so

//Path to a file.
OPENNI_INCLUDE_DIR:PATH=/usr/include/ni

//Path to a library.
OPENNI_LIBRARY:FILEPATH=/usr/lib/libOpenNI.so

//Path to a library.
OPENSSL_CRYPTO_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libcrypto.so

//Path to a file.
OPENSSL_INCLUDE_DIR:PATH=/usr/include

//Path to a library.
OPENSSL_SSL_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libssl.so

//path to 2d headers
PCL_2D_INCLUDE_DIR:PATH=/usr/include/pcl-1.12

//path to apps headers
PCL_APPS_INCLUDE_DIR:PATH=/usr/include/pcl-1.12

//path to pcl_apps library
PCL_APPS_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_apps.so

//path to pcl_apps library debug
PCL_APPS_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_apps.so

//path to common headers
PCL_COMMON_INCLUDE_DIR:PATH=/usr/include/pcl-1.12

//path to pcl_common library
PCL_COMMON_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_common.so

//path to pcl_common library debug
PCL_COMMON_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_common.so

//The directory containing a CMake configuration file for PCL.
PCL_DIR:PATH=/usr/lib/x86_64-linux-gnu/cmake/pcl

//path to features headers
PCL_FEATURES_INCLUDE_DIR:PATH=/usr/include/pcl-1.12

//path to pcl_features library
PCL_FEATURES_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_features.so

//path to pcl_features library debug
PCL_FEATURES_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_features.so

//path to filters headers
PCL_FILTERS_INCLUDE_DIR:PATH=/usr/include/pcl-1.12

//path to pcl_filters library
PCL_FILTERS_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_filters.so

//path to pcl_filters library debug
PCL_FILTERS_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_filters.so

//path to geometry headers
PCL_GEOMETRY_INCLUDE_DIR:PATH=/usr/include/pcl-1.12

//path to in_hand_scanner headers
PCL_IN_HAND_SCANNER_INCLUDE_DIR:PATH=/usr/include/pcl-1.12

//path to io headers
PCL_IO_INCLUDE_DIR:PATH=/usr/include/pcl-1.12

//path to pcl_io library
PCL_IO_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_io.so

//path to pcl_io library debug
PCL_IO_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_io.so

//path to kdtree headers
PCL_KDTREE_INCLUDE_DIR:PATH=/usr/include/pcl-1.12

//path to pcl_kdtree library
PCL_KDTREE_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_kdtree.so

//path to pcl_kdtree library debug
PCL_KDTREE_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_kdtree.so

//path to keypoints headers
PCL_KEYPOINTS_INCLUDE_DIR:PATH=/usr/include/pcl-1.12

//path to pcl_keypoints library
PCL_KEYPOINTS_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_keypoints.so

//path to pcl_keypoints library debug
PCL_KEYPOINTS_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_keypoints.so

//path to ml headers
PCL_ML_INCLUDE_DIR:PATH=/usr/include/pcl-1.12

//path to pcl_ml library
PCL_ML_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_ml.so

//path to pcl_ml library debug
PCL_ML_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_ml.so

//path to modeler headers
PCL_MODELER_INCLUDE_DIR:PATH=/usr/include/pcl-1.12

//path to octree headers
PCL_OCTREE_INCLUDE_DIR:PATH=/usr/include/pcl-1.12

//path to pcl_octree library
PCL_OCTREE_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_octree.so

//path to pcl_octree library debug
PCL_OCTREE_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_octree.so

//path to outofcore headers
PCL_OUTOFCORE_INCLUDE_DIR:PATH=/usr/include/pcl-1.12

//path to pcl_outofcore library
PCL_OUTOFCORE_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_outofcore.so

//path to pcl_outofcore library debug
PCL_OUTOFCORE_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_outofcore.so

//path to people headers
PCL_PEOPLE_INCLUDE_DIR:PATH=/usr/include/pcl-1.12

//path to pcl_people library
PCL_PEOPLE_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_people.so

//path to pcl_people library debug
PCL_PEOPLE_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_people.so

//path to point_cloud_editor headers
PCL_POINT_CLOUD_EDITOR_INCLUDE_DIR:PATH=/usr/include/pcl-1.12

//path to recognition headers
PCL_RECOGNITION_INCLUDE_DIR:PATH=/usr/include/pcl-1.12

//path to pcl_recognition library
PCL_RECOGNITION_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_recognition.so

//path to pcl_recognition library debug
PCL_RECOGNITION_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_recognition.so

//path to registration headers
PCL_REGISTRATION_INCLUDE_DIR:PATH=/usr/include/pcl-1.12

//path to pcl_registration library
PCL_REGISTRATION_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_registration.so

//path to pcl_registration library debug
PCL_REGISTRATION_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_registration.so

//path to sample_consensus headers
PCL_SAMPLE_CONSENSUS_INCLUDE_DIR:PATH=/usr/include/pcl-1.12

//path to pcl_sample_consensus library
PCL_SAMPLE_CONSENSUS_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_sample_consensus.so

//path to pcl_sample_consensus library debug
PCL_SAMPLE_CONSENSUS_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_sample_consensus.so

//path to search headers
PCL_SEARCH_INCLUDE_DIR:PATH=/usr/include/pcl-1.12

//path to pcl_search library
PCL_SEARCH_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_search.so

//path to pcl_search library debug
PCL_SEARCH_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_search.so

//path to segmentation headers
PCL_SEGMENTATION_INCLUDE_DIR:PATH=/usr/include/pcl-1.12

//path to pcl_segmentation library
PCL_SEGMENTATION_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_segmentation.so

//path to pcl_segmentation library debug
PCL_SEGMENTATION_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_segmentation.so

//path to stereo headers
PCL_STEREO_INCLUDE_DIR:PATH=/usr/include/pcl-1.12

//path to pcl_stereo library
PCL_STEREO_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_stereo.so

//path to pcl_stereo library debug
PCL_STEREO_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_stereo.so

//path to surface headers
PCL_SURFACE_INCLUDE_DIR:PATH=/usr/include/pcl-1.12

//path to pcl_surface library
PCL_SURFACE_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_surface.so

//path to pcl_surface library debug
PCL_SURFACE_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_surface.so

//path to tracking headers
PCL_TRACKING_INCLUDE_DIR:PATH=/usr/include/pcl-1.12

//path to pcl_tracking library
PCL_TRACKING_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_tracking.so

//path to pcl_tracking library debug
PCL_TRACKING_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_tracking.so

//path to visualization headers
PCL_VISUALIZATION_INCLUDE_DIR:PATH=/usr/include/pcl-1.12

//path to pcl_visualization library
PCL_VISUALIZATION_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_visualization.so

//path to pcl_visualization library debug
PCL_VISUALIZATION_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_visualization.so

//Arguments to supply to pkg-config
PKG_CONFIG_ARGN:STRING=

//pkg-config executable
PKG_CONFIG_EXECUTABLE:FILEPATH=/usr/bin/pkg-config

//Path to a library.
PNG_LIBRARY_DEBUG:FILEPATH=PNG_LIBRARY_DEBUG-NOTFOUND

//Path to a library.
PNG_LIBRARY_RELEASE:FILEPATH=/usr/lib/x86_64-linux-gnu/libpng.so

//Path to a file.
PNG_PNG_INCLUDE_DIR:PATH=/usr/include

//Path to a program.
Python3_EXECUTABLE:FILEPATH=/usr/bin/python3

//The directory containing a CMake configuration file for Qhull.
Qhull_DIR:PATH=/usr/lib/x86_64-linux-gnu/cmake/Qhull

//The directory containing a CMake configuration file for Qt5Core.
Qt5Core_DIR:PATH=/usr/lib/x86_64-linux-gnu/cmake/Qt5Core

//The directory containing a CMake configuration file for Qt5Gui.
Qt5Gui_DIR:PATH=/usr/lib/x86_64-linux-gnu/cmake/Qt5Gui

//The directory containing a CMake configuration file for Qt5OpenGL.
Qt5OpenGL_DIR:PATH=/usr/lib/x86_64-linux-gnu/cmake/Qt5OpenGL

//The directory containing a CMake configuration file for Qt5Widgets.
Qt5Widgets_DIR:PATH=/usr/lib/x86_64-linux-gnu/cmake/Qt5Widgets

//The directory containing a CMake configuration file for Qt5.
Qt5_DIR:PATH=/usr/lib/x86_64-linux-gnu/cmake/Qt5

//Name of the computer/site where compile is being run
SITE:STRING=fml-ROG-Strix-G533ZW-G533ZW

//The directory containing a CMake configuration file for TBB.
TBB_DIR:PATH=/usr/lib/x86_64-linux-gnu/cmake/TBB

//Path to a file.
TIFF_INCLUDE_DIR:PATH=/usr/include/x86_64-linux-gnu

//Path to a library.
TIFF_LIBRARY_DEBUG:FILEPATH=TIFF_LIBRARY_DEBUG-NOTFOUND

//Path to a library.
TIFF_LIBRARY_RELEASE:FILEPATH=/usr/lib/x86_64-linux-gnu/libtiff.so

//The directory containing a CMake configuration file for TinyXML2.
TinyXML2_DIR:PATH=TinyXML2_DIR-NOTFOUND

//The directory containing VTKConfig.cmake
VTK_DIR:PATH=/usr/lib/x86_64-linux-gnu/cmake/vtk-9.1

//Number of processors available to run parallel tests.
VTK_MPI_NUMPROCS:STRING=2

//Path to a file.
X11_ICE_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_ICE_LIB:FILEPATH=/usr/lib/x86_64-linux-gnu/libICE.so

//Path to a file.
X11_SM_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_SM_LIB:FILEPATH=/usr/lib/x86_64-linux-gnu/libSM.so

//Path to a file.
X11_X11_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_X11_LIB:FILEPATH=/usr/lib/x86_64-linux-gnu/libX11.so

//Path to a file.
X11_X11_xcb_INCLUDE_PATH:PATH=X11_X11_xcb_INCLUDE_PATH-NOTFOUND

//Path to a library.
X11_X11_xcb_LIB:FILEPATH=X11_X11_xcb_LIB-NOTFOUND

//Path to a file.
X11_XRes_INCLUDE_PATH:PATH=X11_XRes_INCLUDE_PATH-NOTFOUND

//Path to a library.
X11_XRes_LIB:FILEPATH=X11_XRes_LIB-NOTFOUND

//Path to a file.
X11_XShm_INCLUDE_PATH:PATH=/usr/include

//Path to a file.
X11_XSync_INCLUDE_PATH:PATH=/usr/include

//Path to a file.
X11_Xaccessrules_INCLUDE_PATH:PATH=X11_Xaccessrules_INCLUDE_PATH-NOTFOUND

//Path to a file.
X11_Xaccessstr_INCLUDE_PATH:PATH=/usr/include

//Path to a file.
X11_Xau_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_Xau_LIB:FILEPATH=/usr/lib/x86_64-linux-gnu/libXau.so

//Path to a file.
X11_Xaw_INCLUDE_PATH:PATH=X11_Xaw_INCLUDE_PATH-NOTFOUND

//Path to a library.
X11_Xaw_LIB:FILEPATH=/usr/lib/x86_64-linux-gnu/libXaw.so

//Path to a file.
X11_Xcomposite_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_Xcomposite_LIB:FILEPATH=/usr/lib/x86_64-linux-gnu/libXcomposite.so

//Path to a file.
X11_Xcursor_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_Xcursor_LIB:FILEPATH=/usr/lib/x86_64-linux-gnu/libXcursor.so

//Path to a file.
X11_Xdamage_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_Xdamage_LIB:FILEPATH=/usr/lib/x86_64-linux-gnu/libXdamage.so

//Path to a file.
X11_Xdmcp_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_Xdmcp_LIB:FILEPATH=/usr/lib/x86_64-linux-gnu/libXdmcp.so

//Path to a file.
X11_Xext_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_Xext_LIB:FILEPATH=/usr/lib/x86_64-linux-gnu/libXext.so

//Path to a file.
X11_Xfixes_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_Xfixes_LIB:FILEPATH=/usr/lib/x86_64-linux-gnu/libXfixes.so

//Path to a file.
X11_Xft_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_Xft_LIB:FILEPATH=/usr/lib/x86_64-linux-gnu/libXft.so

//Path to a file.
X11_Xi_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_Xi_LIB:FILEPATH=/usr/lib/x86_64-linux-gnu/libXi.so

//Path to a file.
X11_Xinerama_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_Xinerama_LIB:FILEPATH=/usr/lib/x86_64-linux-gnu/libXinerama.so

//Path to a file.
X11_Xkb_INCLUDE_PATH:PATH=/usr/include

//Path to a file.
X11_Xkblib_INCLUDE_PATH:PATH=/usr/include

//Path to a file.
X11_Xlib_INCLUDE_PATH:PATH=/usr/include

//Path to a file.
X11_Xmu_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_Xmu_LIB:FILEPATH=/usr/lib/x86_64-linux-gnu/libXmu.so

//Path to a file.
X11_Xpm_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_Xpm_LIB:FILEPATH=/usr/lib/x86_64-linux-gnu/libXpm.so

//Path to a file.
X11_Xrandr_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_Xrandr_LIB:FILEPATH=/usr/lib/x86_64-linux-gnu/libXrandr.so

//Path to a file.
X11_Xrender_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_Xrender_LIB:FILEPATH=/usr/lib/x86_64-linux-gnu/libXrender.so

//Path to a file.
X11_Xshape_INCLUDE_PATH:PATH=/usr/include

//Path to a file.
X11_Xss_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_Xss_LIB:FILEPATH=/usr/lib/x86_64-linux-gnu/libXss.so

//Path to a file.
X11_Xt_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_Xt_LIB:FILEPATH=/usr/lib/x86_64-linux-gnu/libXt.so

//Path to a file.
X11_Xtst_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_Xtst_LIB:FILEPATH=/usr/lib/x86_64-linux-gnu/libXtst.so

//Path to a file.
X11_Xutil_INCLUDE_PATH:PATH=/usr/include

//Path to a file.
X11_Xv_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_Xv_LIB:FILEPATH=/usr/lib/x86_64-linux-gnu/libXv.so

//Path to a file.
X11_Xxf86misc_INCLUDE_PATH:PATH=X11_Xxf86misc_INCLUDE_PATH-NOTFOUND

//Path to a library.
X11_Xxf86misc_LIB:FILEPATH=X11_Xxf86misc_LIB-NOTFOUND

//Path to a file.
X11_Xxf86vm_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_Xxf86vm_LIB:FILEPATH=/usr/lib/x86_64-linux-gnu/libXxf86vm.so

//Path to a file.
X11_dpms_INCLUDE_PATH:PATH=/usr/include

//Path to a file.
X11_xcb_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_xcb_LIB:FILEPATH=/usr/lib/x86_64-linux-gnu/libxcb.so

//Path to a file.
X11_xcb_icccm_INCLUDE_PATH:PATH=X11_xcb_icccm_INCLUDE_PATH-NOTFOUND

//Path to a library.
X11_xcb_icccm_LIB:FILEPATH=X11_xcb_icccm_LIB-NOTFOUND

//Path to a file.
X11_xcb_util_INCLUDE_PATH:PATH=X11_xcb_util_INCLUDE_PATH-NOTFOUND

//Path to a library.
X11_xcb_util_LIB:FILEPATH=X11_xcb_util_LIB-NOTFOUND

//Path to a file.
X11_xcb_xfixes_INCLUDE_PATH:PATH=X11_xcb_xfixes_INCLUDE_PATH-NOTFOUND

//Path to a library.
X11_xcb_xfixes_LIB:FILEPATH=X11_xcb_xfixes_LIB-NOTFOUND

//Path to a library.
X11_xcb_xkb_LIB:FILEPATH=X11_xcb_xkb_LIB-NOTFOUND

//Path to a file.
X11_xkbcommon_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_xkbcommon_LIB:FILEPATH=/usr/lib/x86_64-linux-gnu/libxkbcommon.so

//Path to a file.
X11_xkbcommon_X11_INCLUDE_PATH:PATH=X11_xkbcommon_X11_INCLUDE_PATH-NOTFOUND

//Path to a library.
X11_xkbcommon_X11_LIB:FILEPATH=X11_xkbcommon_X11_LIB-NOTFOUND

//Path to a file.
X11_xkbfile_INCLUDE_PATH:PATH=X11_xkbfile_INCLUDE_PATH-NOTFOUND

//Path to a library.
X11_xkbfile_LIB:FILEPATH=X11_xkbfile_LIB-NOTFOUND

//Path to a file.
ZLIB_INCLUDE_DIR:PATH=/usr/include

//Path to a library.
ZLIB_LIBRARY_DEBUG:FILEPATH=ZLIB_LIBRARY_DEBUG-NOTFOUND

//Path to a library.
ZLIB_LIBRARY_RELEASE:FILEPATH=/usr/lib/x86_64-linux-gnu/libz.so

//Path to a library.
_lib:FILEPATH=/opt/ros/humble/lib/liblaser_geometry.so

//The directory containing a CMake configuration file for action_msgs.
action_msgs_DIR:PATH=/opt/ros/humble/share/action_msgs/cmake

//The directory containing a CMake configuration file for ament_cmake.
ament_cmake_DIR:PATH=/opt/ros/humble/share/ament_cmake/cmake

//The directory containing a CMake configuration file for ament_cmake_core.
ament_cmake_core_DIR:PATH=/opt/ros/humble/share/ament_cmake_core/cmake

//The directory containing a CMake configuration file for ament_cmake_cppcheck.
ament_cmake_cppcheck_DIR:PATH=/opt/ros/humble/share/ament_cmake_cppcheck/cmake

//The directory containing a CMake configuration file for ament_cmake_export_definitions.
ament_cmake_export_definitions_DIR:PATH=/opt/ros/humble/share/ament_cmake_export_definitions/cmake

//The directory containing a CMake configuration file for ament_cmake_export_dependencies.
ament_cmake_export_dependencies_DIR:PATH=/opt/ros/humble/share/ament_cmake_export_dependencies/cmake

//The directory containing a CMake configuration file for ament_cmake_export_include_directories.
ament_cmake_export_include_directories_DIR:PATH=/opt/ros/humble/share/ament_cmake_export_include_directories/cmake

//The directory containing a CMake configuration file for ament_cmake_export_interfaces.
ament_cmake_export_interfaces_DIR:PATH=/opt/ros/humble/share/ament_cmake_export_interfaces/cmake

//The directory containing a CMake configuration file for ament_cmake_export_libraries.
ament_cmake_export_libraries_DIR:PATH=/opt/ros/humble/share/ament_cmake_export_libraries/cmake

//The directory containing a CMake configuration file for ament_cmake_export_link_flags.
ament_cmake_export_link_flags_DIR:PATH=/opt/ros/humble/share/ament_cmake_export_link_flags/cmake

//The directory containing a CMake configuration file for ament_cmake_export_targets.
ament_cmake_export_targets_DIR:PATH=/opt/ros/humble/share/ament_cmake_export_targets/cmake

//The directory containing a CMake configuration file for ament_cmake_flake8.
ament_cmake_flake8_DIR:PATH=/opt/ros/humble/share/ament_cmake_flake8/cmake

//The directory containing a CMake configuration file for ament_cmake_gen_version_h.
ament_cmake_gen_version_h_DIR:PATH=/opt/ros/humble/share/ament_cmake_gen_version_h/cmake

//The directory containing a CMake configuration file for ament_cmake_include_directories.
ament_cmake_include_directories_DIR:PATH=/opt/ros/humble/share/ament_cmake_include_directories/cmake

//The directory containing a CMake configuration file for ament_cmake_libraries.
ament_cmake_libraries_DIR:PATH=/opt/ros/humble/share/ament_cmake_libraries/cmake

//The directory containing a CMake configuration file for ament_cmake_lint_cmake.
ament_cmake_lint_cmake_DIR:PATH=/opt/ros/humble/share/ament_cmake_lint_cmake/cmake

//The directory containing a CMake configuration file for ament_cmake_pep257.
ament_cmake_pep257_DIR:PATH=/opt/ros/humble/share/ament_cmake_pep257/cmake

//The directory containing a CMake configuration file for ament_cmake_python.
ament_cmake_python_DIR:PATH=/opt/ros/humble/share/ament_cmake_python/cmake

//The directory containing a CMake configuration file for ament_cmake_target_dependencies.
ament_cmake_target_dependencies_DIR:PATH=/opt/ros/humble/share/ament_cmake_target_dependencies/cmake

//The directory containing a CMake configuration file for ament_cmake_test.
ament_cmake_test_DIR:PATH=/opt/ros/humble/share/ament_cmake_test/cmake

//The directory containing a CMake configuration file for ament_cmake_uncrustify.
ament_cmake_uncrustify_DIR:PATH=/opt/ros/humble/share/ament_cmake_uncrustify/cmake

//The directory containing a CMake configuration file for ament_cmake_version.
ament_cmake_version_DIR:PATH=/opt/ros/humble/share/ament_cmake_version/cmake

//The directory containing a CMake configuration file for ament_cmake_xmllint.
ament_cmake_xmllint_DIR:PATH=/opt/ros/humble/share/ament_cmake_xmllint/cmake

//Path to a program.
ament_cppcheck_BIN:FILEPATH=/opt/ros/humble/bin/ament_cppcheck

//Path to a program.
ament_flake8_BIN:FILEPATH=/opt/ros/humble/bin/ament_flake8

//The directory containing a CMake configuration file for ament_index_cpp.
ament_index_cpp_DIR:PATH=/opt/ros/humble/share/ament_index_cpp/cmake

//The directory containing a CMake configuration file for ament_lint_auto.
ament_lint_auto_DIR:PATH=/opt/ros/humble/share/ament_lint_auto/cmake

//Path to a program.
ament_lint_cmake_BIN:FILEPATH=/opt/ros/humble/bin/ament_lint_cmake

//The directory containing a CMake configuration file for ament_lint_common.
ament_lint_common_DIR:PATH=/opt/ros/humble/share/ament_lint_common/cmake

//Path to a program.
ament_pep257_BIN:FILEPATH=/opt/ros/humble/bin/ament_pep257

//Path to a program.
ament_uncrustify_BIN:FILEPATH=/opt/ros/humble/bin/ament_uncrustify

//Path to a program.
ament_xmllint_BIN:FILEPATH=/opt/ros/humble/bin/ament_xmllint

//The directory containing a CMake configuration file for boost_date_time.
boost_date_time_DIR:PATH=/usr/lib/x86_64-linux-gnu/cmake/boost_date_time-1.74.0

//The directory containing a CMake configuration file for boost_filesystem.
boost_filesystem_DIR:PATH=/usr/lib/x86_64-linux-gnu/cmake/boost_filesystem-1.74.0

//The directory containing a CMake configuration file for boost_headers.
boost_headers_DIR:PATH=/usr/lib/x86_64-linux-gnu/cmake/boost_headers-1.74.0

//The directory containing a CMake configuration file for boost_iostreams.
boost_iostreams_DIR:PATH=/usr/lib/x86_64-linux-gnu/cmake/boost_iostreams-1.74.0

//The directory containing a CMake configuration file for boost_serialization.
boost_serialization_DIR:PATH=/usr/lib/x86_64-linux-gnu/cmake/boost_serialization-1.74.0

//The directory containing a CMake configuration file for boost_system.
boost_system_DIR:PATH=/usr/lib/x86_64-linux-gnu/cmake/boost_system-1.74.0

//The directory containing a CMake configuration file for builtin_interfaces.
builtin_interfaces_DIR:PATH=/opt/ros/humble/share/builtin_interfaces/cmake

//The directory containing a CMake configuration file for class_loader.
class_loader_DIR:PATH=/opt/ros/humble/share/class_loader/cmake

//The directory containing a CMake configuration file for composition_interfaces.
composition_interfaces_DIR:PATH=/opt/ros/humble/share/composition_interfaces/cmake

//The directory containing a CMake configuration file for console_bridge.
console_bridge_DIR:PATH=/usr/lib/x86_64-linux-gnu/console_bridge/cmake

//double-conversion include directory
double-conversion_INCLUDE_DIR:PATH=/usr/include/double-conversion

//double-conversion library
double-conversion_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libdouble-conversion.so

//The directory containing a CMake configuration file for eigen3_cmake_module.
eigen3_cmake_module_DIR:PATH=/opt/ros/humble/share/eigen3_cmake_module/cmake

//The directory containing a CMake configuration file for fastcdr.
fastcdr_DIR:PATH=/opt/ros/humble/lib/cmake/fastcdr

//The directory containing a CMake configuration file for fastrtps.
fastrtps_DIR:PATH=/opt/ros/humble/share/fastrtps/cmake

//The directory containing a CMake configuration file for fastrtps_cmake_module.
fastrtps_cmake_module_DIR:PATH=/opt/ros/humble/share/fastrtps_cmake_module/cmake

//The directory containing a CMake configuration file for flann.
flann_DIR:PATH=flann_DIR-NOTFOUND

//The directory containing a CMake configuration file for fmt.
fmt_DIR:PATH=/usr/lib/x86_64-linux-gnu/cmake/fmt

//The directory containing a CMake configuration file for foonathan_memory.
foonathan_memory_DIR:PATH=/opt/ros/humble/lib/foonathan_memory/cmake

//The directory containing a CMake configuration file for geometry_msgs.
geometry_msgs_DIR:PATH=/opt/ros/humble/share/geometry_msgs/cmake

//The directory containing a CMake configuration file for laser_geometry.
laser_geometry_DIR:PATH=/opt/ros/humble/share/laser_geometry/cmake

//The directory containing a CMake configuration file for libstatistics_collector.
libstatistics_collector_DIR:PATH=/opt/ros/humble/share/libstatistics_collector/cmake

//Path to a file.
libusb_INCLUDE_DIR:PATH=/usr/include

//Path to a library.
libusb_LIBRARIES:FILEPATH=/usr/lib/x86_64-linux-gnu/libusb-1.0.so

//The directory containing a CMake configuration file for libyaml_vendor.
libyaml_vendor_DIR:PATH=/opt/ros/humble/share/libyaml_vendor/cmake

//The directory containing a CMake configuration file for message_filters.
message_filters_DIR:PATH=/opt/ros/humble/share/message_filters/cmake

//The directory containing a CMake configuration file for orocos_kdl.
orocos_kdl_DIR:PATH=/usr/lib/x86_64-linux-gnu/cmake/orocos_kdl

//Path to a library.
orocos_kdl_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/liborocos-kdl.so

//The directory containing a CMake configuration file for orocos_kdl_vendor.
orocos_kdl_vendor_DIR:PATH=/opt/ros/humble/share/orocos_kdl_vendor/cmake

//The directory containing a CMake configuration file for pcl_conversions.
pcl_conversions_DIR:PATH=/opt/ros/humble/share/pcl_conversions/cmake

//The directory containing a CMake configuration file for pcl_msgs.
pcl_msgs_DIR:PATH=/opt/ros/humble/share/pcl_msgs/cmake

//The directory containing a CMake configuration file for pcl_ros.
pcl_ros_DIR:PATH=/opt/ros/humble/share/pcl_ros/cmake

//Path to a library.
pkgcfg_lib_PC_EXPAT_expat:FILEPATH=/usr/lib/x86_64-linux-gnu/libexpat.so

//Path to a library.
pkgcfg_lib_PC_EXPAT_m:FILEPATH=/usr/lib/x86_64-linux-gnu/libm.so

//Path to a library.
pkgcfg_lib_PC_FLANN_flann:FILEPATH=/usr/lib/x86_64-linux-gnu/libflann.so

//Path to a library.
pkgcfg_lib_PC_FLANN_flann_cpp:FILEPATH=/usr/lib/x86_64-linux-gnu/libflann_cpp.so

//Path to a library.
pkgcfg_lib_PC_FLANN_lz4:FILEPATH=/usr/lib/x86_64-linux-gnu/liblz4.so

//Path to a library.
pkgcfg_lib_PC_OPENNI2_OpenNI2:FILEPATH=/usr/lib/x86_64-linux-gnu/libOpenNI2.so

//Path to a library.
pkgcfg_lib_PC_OPENNI_OpenNI:FILEPATH=/usr/lib/libOpenNI.so

//Path to a library.
pkgcfg_lib_PC_libusb_usb-1.0:FILEPATH=/usr/lib/x86_64-linux-gnu/libusb-1.0.so

//Path to a library.
pkgcfg_lib_PKG_FONTCONFIG_fontconfig:FILEPATH=/usr/lib/x86_64-linux-gnu/libfontconfig.so

//Path to a library.
pkgcfg_lib_PKG_FONTCONFIG_freetype:FILEPATH=/usr/lib/x86_64-linux-gnu/libfreetype.so

//Path to a library.
pkgcfg_lib__OPENSSL_crypto:FILEPATH=/usr/lib/x86_64-linux-gnu/libcrypto.so

//Path to a library.
pkgcfg_lib__OPENSSL_ssl:FILEPATH=/usr/lib/x86_64-linux-gnu/libssl.so

//Value Computed by CMake
pointcloud_merger_BINARY_DIR:STATIC=/home/<USER>/robot/robot_ws/build/pointcloud_merger

//Value Computed by CMake
pointcloud_merger_IS_TOP_LEVEL:STATIC=ON

//Value Computed by CMake
pointcloud_merger_SOURCE_DIR:STATIC=/home/<USER>/robot/robot_ws/src/pointcloud_merger

//The directory containing a CMake configuration file for rcl.
rcl_DIR:PATH=/opt/ros/humble/share/rcl/cmake

//The directory containing a CMake configuration file for rcl_action.
rcl_action_DIR:PATH=/opt/ros/humble/share/rcl_action/cmake

//The directory containing a CMake configuration file for rcl_interfaces.
rcl_interfaces_DIR:PATH=/opt/ros/humble/share/rcl_interfaces/cmake

//The directory containing a CMake configuration file for rcl_logging_interface.
rcl_logging_interface_DIR:PATH=/opt/ros/humble/share/rcl_logging_interface/cmake

//The directory containing a CMake configuration file for rcl_logging_spdlog.
rcl_logging_spdlog_DIR:PATH=/opt/ros/humble/share/rcl_logging_spdlog/cmake

//The directory containing a CMake configuration file for rcl_yaml_param_parser.
rcl_yaml_param_parser_DIR:PATH=/opt/ros/humble/share/rcl_yaml_param_parser/cmake

//The directory containing a CMake configuration file for rclcpp.
rclcpp_DIR:PATH=/opt/ros/humble/share/rclcpp/cmake

//The directory containing a CMake configuration file for rclcpp_action.
rclcpp_action_DIR:PATH=/opt/ros/humble/share/rclcpp_action/cmake

//The directory containing a CMake configuration file for rclcpp_components.
rclcpp_components_DIR:PATH=/opt/ros/humble/share/rclcpp_components/cmake

//The directory containing a CMake configuration file for rcpputils.
rcpputils_DIR:PATH=/opt/ros/humble/share/rcpputils/cmake

//The directory containing a CMake configuration file for rcutils.
rcutils_DIR:PATH=/opt/ros/humble/share/rcutils/cmake

//The directory containing a CMake configuration file for rmw.
rmw_DIR:PATH=/opt/ros/humble/share/rmw/cmake

//The directory containing a CMake configuration file for rmw_dds_common.
rmw_dds_common_DIR:PATH=/opt/ros/humble/share/rmw_dds_common/cmake

//The directory containing a CMake configuration file for rmw_fastrtps_cpp.
rmw_fastrtps_cpp_DIR:PATH=/opt/ros/humble/share/rmw_fastrtps_cpp/cmake

//The directory containing a CMake configuration file for rmw_fastrtps_shared_cpp.
rmw_fastrtps_shared_cpp_DIR:PATH=/opt/ros/humble/share/rmw_fastrtps_shared_cpp/cmake

//The directory containing a CMake configuration file for rmw_implementation.
rmw_implementation_DIR:PATH=/opt/ros/humble/share/rmw_implementation/cmake

//The directory containing a CMake configuration file for rmw_implementation_cmake.
rmw_implementation_cmake_DIR:PATH=/opt/ros/humble/share/rmw_implementation_cmake/cmake

//The directory containing a CMake configuration file for rosgraph_msgs.
rosgraph_msgs_DIR:PATH=/opt/ros/humble/share/rosgraph_msgs/cmake

//The directory containing a CMake configuration file for rosidl_adapter.
rosidl_adapter_DIR:PATH=/opt/ros/humble/share/rosidl_adapter/cmake

//The directory containing a CMake configuration file for rosidl_cmake.
rosidl_cmake_DIR:PATH=/opt/ros/humble/share/rosidl_cmake/cmake

//The directory containing a CMake configuration file for rosidl_default_runtime.
rosidl_default_runtime_DIR:PATH=/opt/ros/humble/share/rosidl_default_runtime/cmake

//The directory containing a CMake configuration file for rosidl_generator_c.
rosidl_generator_c_DIR:PATH=/opt/ros/humble/share/rosidl_generator_c/cmake

//The directory containing a CMake configuration file for rosidl_generator_cpp.
rosidl_generator_cpp_DIR:PATH=/opt/ros/humble/share/rosidl_generator_cpp/cmake

//The directory containing a CMake configuration file for rosidl_runtime_c.
rosidl_runtime_c_DIR:PATH=/opt/ros/humble/share/rosidl_runtime_c/cmake

//The directory containing a CMake configuration file for rosidl_runtime_cpp.
rosidl_runtime_cpp_DIR:PATH=/opt/ros/humble/share/rosidl_runtime_cpp/cmake

//The directory containing a CMake configuration file for rosidl_typesupport_c.
rosidl_typesupport_c_DIR:PATH=/opt/ros/humble/share/rosidl_typesupport_c/cmake

//The directory containing a CMake configuration file for rosidl_typesupport_cpp.
rosidl_typesupport_cpp_DIR:PATH=/opt/ros/humble/share/rosidl_typesupport_cpp/cmake

//The directory containing a CMake configuration file for rosidl_typesupport_fastrtps_c.
rosidl_typesupport_fastrtps_c_DIR:PATH=/opt/ros/humble/share/rosidl_typesupport_fastrtps_c/cmake

//The directory containing a CMake configuration file for rosidl_typesupport_fastrtps_cpp.
rosidl_typesupport_fastrtps_cpp_DIR:PATH=/opt/ros/humble/share/rosidl_typesupport_fastrtps_cpp/cmake

//The directory containing a CMake configuration file for rosidl_typesupport_interface.
rosidl_typesupport_interface_DIR:PATH=/opt/ros/humble/share/rosidl_typesupport_interface/cmake

//The directory containing a CMake configuration file for rosidl_typesupport_introspection_c.
rosidl_typesupport_introspection_c_DIR:PATH=/opt/ros/humble/share/rosidl_typesupport_introspection_c/cmake

//The directory containing a CMake configuration file for rosidl_typesupport_introspection_cpp.
rosidl_typesupport_introspection_cpp_DIR:PATH=/opt/ros/humble/share/rosidl_typesupport_introspection_cpp/cmake

//The directory containing a CMake configuration file for sensor_msgs.
sensor_msgs_DIR:PATH=/opt/ros/humble/share/sensor_msgs/cmake

//The directory containing a CMake configuration file for spdlog.
spdlog_DIR:PATH=/usr/lib/x86_64-linux-gnu/cmake/spdlog

//The directory containing a CMake configuration file for spdlog_vendor.
spdlog_vendor_DIR:PATH=/opt/ros/humble/share/spdlog_vendor/cmake

//The directory containing a CMake configuration file for statistics_msgs.
statistics_msgs_DIR:PATH=/opt/ros/humble/share/statistics_msgs/cmake

//The directory containing a CMake configuration file for std_msgs.
std_msgs_DIR:PATH=/opt/ros/humble/share/std_msgs/cmake

//The directory containing a CMake configuration file for tf2.
tf2_DIR:PATH=/opt/ros/humble/share/tf2/cmake

//The directory containing a CMake configuration file for tf2_eigen.
tf2_eigen_DIR:PATH=/opt/ros/humble/share/tf2_eigen/cmake

//The directory containing a CMake configuration file for tf2_geometry_msgs.
tf2_geometry_msgs_DIR:PATH=/opt/ros/humble/share/tf2_geometry_msgs/cmake

//The directory containing a CMake configuration file for tf2_msgs.
tf2_msgs_DIR:PATH=/opt/ros/humble/share/tf2_msgs/cmake

//The directory containing a CMake configuration file for tf2_ros.
tf2_ros_DIR:PATH=/opt/ros/humble/share/tf2_ros/cmake

//The directory containing a CMake configuration file for tf2_sensor_msgs.
tf2_sensor_msgs_DIR:PATH=/opt/ros/humble/share/tf2_sensor_msgs/cmake

//The directory containing a CMake configuration file for tracetools.
tracetools_DIR:PATH=/opt/ros/humble/share/tracetools/cmake

//The directory containing a CMake configuration file for unique_identifier_msgs.
unique_identifier_msgs_DIR:PATH=/opt/ros/humble/share/unique_identifier_msgs/cmake

//utf8cpp include directory
utf8cpp_INCLUDE_DIR:PATH=/usr/include/utf8cpp

//Path to a program.
xmllint_BIN:FILEPATH=/usr/bin/xmllint

//The directory containing a CMake configuration file for yaml.
yaml_DIR:PATH=/opt/ros/humble/cmake


########################
# INTERNAL cache entries
########################

//ADVANCED property for variable: Boost_DIR
Boost_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_ADDR2LINE
CMAKE_ADDR2LINE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_AR
CMAKE_AR-ADVANCED:INTERNAL=1
//This is the directory where this CMakeCache.txt was created
CMAKE_CACHEFILE_DIR:INTERNAL=/home/<USER>/robot/robot_ws/build/pointcloud_merger
//Major version of cmake used to create the current loaded cache
CMAKE_CACHE_MAJOR_VERSION:INTERNAL=3
//Minor version of cmake used to create the current loaded cache
CMAKE_CACHE_MINOR_VERSION:INTERNAL=22
//Patch version of cmake used to create the current loaded cache
CMAKE_CACHE_PATCH_VERSION:INTERNAL=1
//ADVANCED property for variable: CMAKE_COLOR_MAKEFILE
CMAKE_COLOR_MAKEFILE-ADVANCED:INTERNAL=1
//Path to CMake executable.
CMAKE_COMMAND:INTERNAL=/usr/bin/cmake
//Path to cpack program executable.
CMAKE_CPACK_COMMAND:INTERNAL=/usr/bin/cpack
//Path to ctest program executable.
CMAKE_CTEST_COMMAND:INTERNAL=/usr/bin/ctest
//ADVANCED property for variable: CMAKE_CXX_COMPILER
CMAKE_CXX_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_COMPILER_AR
CMAKE_CXX_COMPILER_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_COMPILER_RANLIB
CMAKE_CXX_COMPILER_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS
CMAKE_CXX_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_DEBUG
CMAKE_CXX_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_MINSIZEREL
CMAKE_CXX_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELEASE
CMAKE_CXX_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELWITHDEBINFO
CMAKE_CXX_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER
CMAKE_C_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER_AR
CMAKE_C_COMPILER_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER_RANLIB
CMAKE_C_COMPILER_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS
CMAKE_C_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_DEBUG
CMAKE_C_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_MINSIZEREL
CMAKE_C_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELEASE
CMAKE_C_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELWITHDEBINFO
CMAKE_C_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_DLLTOOL
CMAKE_DLLTOOL-ADVANCED:INTERNAL=1
//Executable file format
CMAKE_EXECUTABLE_FORMAT:INTERNAL=ELF
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS
CMAKE_EXE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_DEBUG
CMAKE_EXE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_MINSIZEREL
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELEASE
CMAKE_EXE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXPORT_COMPILE_COMMANDS
CMAKE_EXPORT_COMPILE_COMMANDS-ADVANCED:INTERNAL=1
//Name of external makefile project generator.
CMAKE_EXTRA_GENERATOR:INTERNAL=
//Name of generator.
CMAKE_GENERATOR:INTERNAL=Unix Makefiles
//Generator instance identifier.
CMAKE_GENERATOR_INSTANCE:INTERNAL=
//Name of generator platform.
CMAKE_GENERATOR_PLATFORM:INTERNAL=
//Name of generator toolset.
CMAKE_GENERATOR_TOOLSET:INTERNAL=
//Have function connect
CMAKE_HAVE_CONNECT:INTERNAL=1
//Have function gethostbyname
CMAKE_HAVE_GETHOSTBYNAME:INTERNAL=1
//Test CMAKE_HAVE_LIBC_PTHREAD
CMAKE_HAVE_LIBC_PTHREAD:INTERNAL=1
//Have include pthread.h
CMAKE_HAVE_PTHREAD_H:INTERNAL=1
//Have function remove
CMAKE_HAVE_REMOVE:INTERNAL=1
//Have function shmat
CMAKE_HAVE_SHMAT:INTERNAL=1
//Source directory with the top level CMakeLists.txt file for this
// project
CMAKE_HOME_DIRECTORY:INTERNAL=/home/<USER>/robot/robot_ws/src/pointcloud_merger
//Install .so files without execute permission.
CMAKE_INSTALL_SO_NO_EXE:INTERNAL=1
//Have library ICE
CMAKE_LIB_ICE_HAS_ICECONNECTIONNUMBER:INTERNAL=1
//ADVANCED property for variable: CMAKE_LINKER
CMAKE_LINKER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MAKE_PROGRAM
CMAKE_MAKE_PROGRAM-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS
CMAKE_MODULE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_DEBUG
CMAKE_MODULE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELEASE
CMAKE_MODULE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_NM
CMAKE_NM-ADVANCED:INTERNAL=1
//number of local generators
CMAKE_NUMBER_OF_MAKEFILES:INTERNAL=1
//ADVANCED property for variable: CMAKE_OBJCOPY
CMAKE_OBJCOPY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_OBJDUMP
CMAKE_OBJDUMP-ADVANCED:INTERNAL=1
//Platform information initialized
CMAKE_PLATFORM_INFO_INITIALIZED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RANLIB
CMAKE_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_READELF
CMAKE_READELF-ADVANCED:INTERNAL=1
//Path to CMake installation.
CMAKE_ROOT:INTERNAL=/usr/share/cmake-3.22
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS
CMAKE_SHARED_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_DEBUG
CMAKE_SHARED_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELEASE
CMAKE_SHARED_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_INSTALL_RPATH
CMAKE_SKIP_INSTALL_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_RPATH
CMAKE_SKIP_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS
CMAKE_STATIC_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_DEBUG
CMAKE_STATIC_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELEASE
CMAKE_STATIC_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STRIP
CMAKE_STRIP-ADVANCED:INTERNAL=1
//uname command
CMAKE_UNAME:INTERNAL=/usr/bin/uname
//ADVANCED property for variable: CMAKE_VERBOSE_MAKEFILE
CMAKE_VERBOSE_MAKEFILE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: EIGEN_INCLUDE_DIR
EIGEN_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: EXPAT_INCLUDE_DIR
EXPAT_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: EXPAT_LIBRARY
EXPAT_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Eigen3_INCLUDE_DIR
Eigen3_INCLUDE_DIR-ADVANCED:INTERNAL=1
//Details about finding Eigen
FIND_PACKAGE_MESSAGE_DETAILS_Eigen:INTERNAL=[/usr/include/eigen3][v(3.1)]
//Details about finding Eigen3
FIND_PACKAGE_MESSAGE_DETAILS_Eigen3:INTERNAL=[1][v3.4.0()]
//Details about finding FLANN
FIND_PACKAGE_MESSAGE_DETAILS_FLANN:INTERNAL=[/usr/lib/x86_64-linux-gnu/libflann_cpp.so][/usr/include][v()]
//Details about finding FastRTPS
FIND_PACKAGE_MESSAGE_DETAILS_FastRTPS:INTERNAL=[/opt/ros/humble/include][/opt/ros/humble/lib/libfastrtps.so;/opt/ros/humble/lib/libfastcdr.so][v()]
//Details about finding OpenSSL
FIND_PACKAGE_MESSAGE_DETAILS_OpenSSL:INTERNAL=[/usr/lib/x86_64-linux-gnu/libcrypto.so][/usr/include][c ][v3.0.2()]
//Details about finding PCL_2D
FIND_PACKAGE_MESSAGE_DETAILS_PCL_2D:INTERNAL=[/usr/include/pcl-1.12][v()]
//Details about finding PCL_APPS
FIND_PACKAGE_MESSAGE_DETAILS_PCL_APPS:INTERNAL=[/usr/lib/x86_64-linux-gnu/libpcl_apps.so][/usr/include/pcl-1.12][v()]
//Details about finding PCL_COMMON
FIND_PACKAGE_MESSAGE_DETAILS_PCL_COMMON:INTERNAL=[/usr/lib/x86_64-linux-gnu/libpcl_common.so][/usr/include/pcl-1.12][v()]
//Details about finding PCL_FEATURES
FIND_PACKAGE_MESSAGE_DETAILS_PCL_FEATURES:INTERNAL=[/usr/lib/x86_64-linux-gnu/libpcl_features.so][/usr/include/pcl-1.12][v()]
//Details about finding PCL_FILTERS
FIND_PACKAGE_MESSAGE_DETAILS_PCL_FILTERS:INTERNAL=[/usr/lib/x86_64-linux-gnu/libpcl_filters.so][/usr/include/pcl-1.12][v()]
//Details about finding PCL_GEOMETRY
FIND_PACKAGE_MESSAGE_DETAILS_PCL_GEOMETRY:INTERNAL=[/usr/include/pcl-1.12][v()]
//Details about finding PCL_IN_HAND_SCANNER
FIND_PACKAGE_MESSAGE_DETAILS_PCL_IN_HAND_SCANNER:INTERNAL=[/usr/include/pcl-1.12][v()]
//Details about finding PCL_IO
FIND_PACKAGE_MESSAGE_DETAILS_PCL_IO:INTERNAL=[/usr/lib/x86_64-linux-gnu/libpcl_io.so][/usr/include/pcl-1.12][v()]
//Details about finding PCL_KDTREE
FIND_PACKAGE_MESSAGE_DETAILS_PCL_KDTREE:INTERNAL=[/usr/lib/x86_64-linux-gnu/libpcl_kdtree.so][/usr/include/pcl-1.12][v()]
//Details about finding PCL_KEYPOINTS
FIND_PACKAGE_MESSAGE_DETAILS_PCL_KEYPOINTS:INTERNAL=[/usr/lib/x86_64-linux-gnu/libpcl_keypoints.so][/usr/include/pcl-1.12][v()]
//Details about finding PCL_ML
FIND_PACKAGE_MESSAGE_DETAILS_PCL_ML:INTERNAL=[/usr/lib/x86_64-linux-gnu/libpcl_ml.so][/usr/include/pcl-1.12][v()]
//Details about finding PCL_MODELER
FIND_PACKAGE_MESSAGE_DETAILS_PCL_MODELER:INTERNAL=[/usr/include/pcl-1.12][v()]
//Details about finding PCL_OCTREE
FIND_PACKAGE_MESSAGE_DETAILS_PCL_OCTREE:INTERNAL=[/usr/lib/x86_64-linux-gnu/libpcl_octree.so][/usr/include/pcl-1.12][v()]
//Details about finding PCL_OUTOFCORE
FIND_PACKAGE_MESSAGE_DETAILS_PCL_OUTOFCORE:INTERNAL=[/usr/lib/x86_64-linux-gnu/libpcl_outofcore.so][/usr/include/pcl-1.12][v()]
//Details about finding PCL_PEOPLE
FIND_PACKAGE_MESSAGE_DETAILS_PCL_PEOPLE:INTERNAL=[/usr/lib/x86_64-linux-gnu/libpcl_people.so][/usr/include/pcl-1.12][v()]
//Details about finding PCL_POINT_CLOUD_EDITOR
FIND_PACKAGE_MESSAGE_DETAILS_PCL_POINT_CLOUD_EDITOR:INTERNAL=[/usr/include/pcl-1.12][v()]
//Details about finding PCL_RECOGNITION
FIND_PACKAGE_MESSAGE_DETAILS_PCL_RECOGNITION:INTERNAL=[/usr/lib/x86_64-linux-gnu/libpcl_recognition.so][/usr/include/pcl-1.12][v()]
//Details about finding PCL_REGISTRATION
FIND_PACKAGE_MESSAGE_DETAILS_PCL_REGISTRATION:INTERNAL=[/usr/lib/x86_64-linux-gnu/libpcl_registration.so][/usr/include/pcl-1.12][v()]
//Details about finding PCL_SAMPLE_CONSENSUS
FIND_PACKAGE_MESSAGE_DETAILS_PCL_SAMPLE_CONSENSUS:INTERNAL=[/usr/lib/x86_64-linux-gnu/libpcl_sample_consensus.so][/usr/include/pcl-1.12][v()]
//Details about finding PCL_SEARCH
FIND_PACKAGE_MESSAGE_DETAILS_PCL_SEARCH:INTERNAL=[/usr/lib/x86_64-linux-gnu/libpcl_search.so][/usr/include/pcl-1.12][v()]
//Details about finding PCL_SEGMENTATION
FIND_PACKAGE_MESSAGE_DETAILS_PCL_SEGMENTATION:INTERNAL=[/usr/lib/x86_64-linux-gnu/libpcl_segmentation.so][/usr/include/pcl-1.12][v()]
//Details about finding PCL_STEREO
FIND_PACKAGE_MESSAGE_DETAILS_PCL_STEREO:INTERNAL=[/usr/lib/x86_64-linux-gnu/libpcl_stereo.so][/usr/include/pcl-1.12][v()]
//Details about finding PCL_SURFACE
FIND_PACKAGE_MESSAGE_DETAILS_PCL_SURFACE:INTERNAL=[/usr/lib/x86_64-linux-gnu/libpcl_surface.so][/usr/include/pcl-1.12][v()]
//Details about finding PCL_TRACKING
FIND_PACKAGE_MESSAGE_DETAILS_PCL_TRACKING:INTERNAL=[/usr/lib/x86_64-linux-gnu/libpcl_tracking.so][/usr/include/pcl-1.12][v()]
//Details about finding PCL_VISUALIZATION
FIND_PACKAGE_MESSAGE_DETAILS_PCL_VISUALIZATION:INTERNAL=[/usr/lib/x86_64-linux-gnu/libpcl_visualization.so][/usr/include/pcl-1.12][v()]
//Details about finding Python3
FIND_PACKAGE_MESSAGE_DETAILS_Python3:INTERNAL=[/usr/bin/python3][cfound components: Interpreter ][v3.10.12()]
//Details about finding Threads
FIND_PACKAGE_MESSAGE_DETAILS_Threads:INTERNAL=[TRUE][v()]
//Details about finding libusb
FIND_PACKAGE_MESSAGE_DETAILS_libusb:INTERNAL=[/usr/lib/x86_64-linux-gnu/libusb-1.0.so][/usr/include][v()]
//ADVANCED property for variable: FREETYPE_INCLUDE_DIR_freetype2
FREETYPE_INCLUDE_DIR_freetype2-ADVANCED:INTERNAL=1
//ADVANCED property for variable: FREETYPE_INCLUDE_DIR_ft2build
FREETYPE_INCLUDE_DIR_ft2build-ADVANCED:INTERNAL=1
//ADVANCED property for variable: FREETYPE_LIBRARY_DEBUG
FREETYPE_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: FREETYPE_LIBRARY_RELEASE
FREETYPE_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Fontconfig_INCLUDE_DIR
Fontconfig_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Fontconfig_LIBRARY
Fontconfig_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: GLEW_INCLUDE_DIR
GLEW_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: GLEW_LIBRARY
GLEW_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: JPEG_INCLUDE_DIR
JPEG_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: JPEG_LIBRARY_DEBUG
JPEG_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: JPEG_LIBRARY_RELEASE
JPEG_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: JsonCpp_INCLUDE_DIR
JsonCpp_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: JsonCpp_LIBRARY
JsonCpp_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: LZ4_INCLUDE_DIR
LZ4_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: LZ4_LIBRARY
LZ4_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: MPIEXEC_EXECUTABLE
MPIEXEC_EXECUTABLE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: MPIEXEC_MAX_NUMPROCS
MPIEXEC_MAX_NUMPROCS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: MPIEXEC_NUMPROC_FLAG
MPIEXEC_NUMPROC_FLAG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: MPIEXEC_POSTFLAGS
MPIEXEC_POSTFLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: MPIEXEC_PREFLAGS
MPIEXEC_PREFLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: MPI_C_ADDITIONAL_INCLUDE_DIRS
MPI_C_ADDITIONAL_INCLUDE_DIRS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: MPI_C_COMPILER
MPI_C_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: MPI_C_COMPILER_INCLUDE_DIRS
MPI_C_COMPILER_INCLUDE_DIRS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: MPI_C_COMPILE_DEFINITIONS
MPI_C_COMPILE_DEFINITIONS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: MPI_C_COMPILE_OPTIONS
MPI_C_COMPILE_OPTIONS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: MPI_C_HEADER_DIR
MPI_C_HEADER_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: MPI_C_LIB_NAMES
MPI_C_LIB_NAMES-ADVANCED:INTERNAL=1
//ADVANCED property for variable: MPI_C_LINK_FLAGS
MPI_C_LINK_FLAGS-ADVANCED:INTERNAL=1
//Result of TRY_COMPILE
MPI_RESULT_C_test_mpi_normal:INTERNAL=TRUE
//ADVANCED property for variable: MPI_mpi_LIBRARY
MPI_mpi_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENGL_EGL_INCLUDE_DIR
OPENGL_EGL_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENGL_GLX_INCLUDE_DIR
OPENGL_GLX_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENGL_INCLUDE_DIR
OPENGL_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENGL_egl_LIBRARY
OPENGL_egl_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENGL_gles2_LIBRARY
OPENGL_gles2_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENGL_gles3_LIBRARY
OPENGL_gles3_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENGL_glu_LIBRARY
OPENGL_glu_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENGL_glx_LIBRARY
OPENGL_glx_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENGL_opengl_LIBRARY
OPENGL_opengl_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENGL_xmesa_INCLUDE_DIR
OPENGL_xmesa_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENSSL_CRYPTO_LIBRARY
OPENSSL_CRYPTO_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENSSL_INCLUDE_DIR
OPENSSL_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENSSL_SSL_LIBRARY
OPENSSL_SSL_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_2D_INCLUDE_DIR
PCL_2D_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_APPS_INCLUDE_DIR
PCL_APPS_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_APPS_LIBRARY
PCL_APPS_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_APPS_LIBRARY_DEBUG
PCL_APPS_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_COMMON_INCLUDE_DIR
PCL_COMMON_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_COMMON_LIBRARY
PCL_COMMON_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_COMMON_LIBRARY_DEBUG
PCL_COMMON_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_FEATURES_INCLUDE_DIR
PCL_FEATURES_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_FEATURES_LIBRARY
PCL_FEATURES_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_FEATURES_LIBRARY_DEBUG
PCL_FEATURES_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_FILTERS_INCLUDE_DIR
PCL_FILTERS_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_FILTERS_LIBRARY
PCL_FILTERS_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_FILTERS_LIBRARY_DEBUG
PCL_FILTERS_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_GEOMETRY_INCLUDE_DIR
PCL_GEOMETRY_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_IN_HAND_SCANNER_INCLUDE_DIR
PCL_IN_HAND_SCANNER_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_IO_INCLUDE_DIR
PCL_IO_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_IO_LIBRARY
PCL_IO_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_IO_LIBRARY_DEBUG
PCL_IO_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_KDTREE_INCLUDE_DIR
PCL_KDTREE_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_KDTREE_LIBRARY
PCL_KDTREE_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_KDTREE_LIBRARY_DEBUG
PCL_KDTREE_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_KEYPOINTS_INCLUDE_DIR
PCL_KEYPOINTS_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_KEYPOINTS_LIBRARY
PCL_KEYPOINTS_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_KEYPOINTS_LIBRARY_DEBUG
PCL_KEYPOINTS_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_ML_INCLUDE_DIR
PCL_ML_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_ML_LIBRARY
PCL_ML_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_ML_LIBRARY_DEBUG
PCL_ML_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_MODELER_INCLUDE_DIR
PCL_MODELER_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_OCTREE_INCLUDE_DIR
PCL_OCTREE_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_OCTREE_LIBRARY
PCL_OCTREE_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_OCTREE_LIBRARY_DEBUG
PCL_OCTREE_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_OUTOFCORE_INCLUDE_DIR
PCL_OUTOFCORE_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_OUTOFCORE_LIBRARY
PCL_OUTOFCORE_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_OUTOFCORE_LIBRARY_DEBUG
PCL_OUTOFCORE_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_PEOPLE_INCLUDE_DIR
PCL_PEOPLE_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_PEOPLE_LIBRARY
PCL_PEOPLE_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_PEOPLE_LIBRARY_DEBUG
PCL_PEOPLE_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_POINT_CLOUD_EDITOR_INCLUDE_DIR
PCL_POINT_CLOUD_EDITOR_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_RECOGNITION_INCLUDE_DIR
PCL_RECOGNITION_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_RECOGNITION_LIBRARY
PCL_RECOGNITION_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_RECOGNITION_LIBRARY_DEBUG
PCL_RECOGNITION_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_REGISTRATION_INCLUDE_DIR
PCL_REGISTRATION_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_REGISTRATION_LIBRARY
PCL_REGISTRATION_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_REGISTRATION_LIBRARY_DEBUG
PCL_REGISTRATION_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_SAMPLE_CONSENSUS_INCLUDE_DIR
PCL_SAMPLE_CONSENSUS_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_SAMPLE_CONSENSUS_LIBRARY
PCL_SAMPLE_CONSENSUS_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_SAMPLE_CONSENSUS_LIBRARY_DEBUG
PCL_SAMPLE_CONSENSUS_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_SEARCH_INCLUDE_DIR
PCL_SEARCH_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_SEARCH_LIBRARY
PCL_SEARCH_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_SEARCH_LIBRARY_DEBUG
PCL_SEARCH_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_SEGMENTATION_INCLUDE_DIR
PCL_SEGMENTATION_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_SEGMENTATION_LIBRARY
PCL_SEGMENTATION_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_SEGMENTATION_LIBRARY_DEBUG
PCL_SEGMENTATION_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_STEREO_INCLUDE_DIR
PCL_STEREO_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_STEREO_LIBRARY
PCL_STEREO_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_STEREO_LIBRARY_DEBUG
PCL_STEREO_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_SURFACE_INCLUDE_DIR
PCL_SURFACE_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_SURFACE_LIBRARY
PCL_SURFACE_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_SURFACE_LIBRARY_DEBUG
PCL_SURFACE_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_TRACKING_INCLUDE_DIR
PCL_TRACKING_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_TRACKING_LIBRARY
PCL_TRACKING_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_TRACKING_LIBRARY_DEBUG
PCL_TRACKING_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_VISUALIZATION_INCLUDE_DIR
PCL_VISUALIZATION_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_VISUALIZATION_LIBRARY
PCL_VISUALIZATION_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_VISUALIZATION_LIBRARY_DEBUG
PCL_VISUALIZATION_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
PC_EIGEN_CFLAGS:INTERNAL=-I/usr/include/eigen3
PC_EIGEN_CFLAGS_I:INTERNAL=
PC_EIGEN_CFLAGS_OTHER:INTERNAL=
PC_EIGEN_FOUND:INTERNAL=1
PC_EIGEN_INCLUDEDIR:INTERNAL=
PC_EIGEN_INCLUDE_DIRS:INTERNAL=/usr/include/eigen3
PC_EIGEN_LDFLAGS:INTERNAL=
PC_EIGEN_LDFLAGS_OTHER:INTERNAL=
PC_EIGEN_LIBDIR:INTERNAL=
PC_EIGEN_LIBRARIES:INTERNAL=
PC_EIGEN_LIBRARY_DIRS:INTERNAL=
PC_EIGEN_LIBS:INTERNAL=
PC_EIGEN_LIBS_L:INTERNAL=
PC_EIGEN_LIBS_OTHER:INTERNAL=
PC_EIGEN_LIBS_PATHS:INTERNAL=
PC_EIGEN_MODULE_NAME:INTERNAL=eigen3
PC_EIGEN_PREFIX:INTERNAL=/usr
PC_EIGEN_STATIC_CFLAGS:INTERNAL=-I/usr/include/eigen3
PC_EIGEN_STATIC_CFLAGS_I:INTERNAL=
PC_EIGEN_STATIC_CFLAGS_OTHER:INTERNAL=
PC_EIGEN_STATIC_INCLUDE_DIRS:INTERNAL=/usr/include/eigen3
PC_EIGEN_STATIC_LDFLAGS:INTERNAL=
PC_EIGEN_STATIC_LDFLAGS_OTHER:INTERNAL=
PC_EIGEN_STATIC_LIBDIR:INTERNAL=
PC_EIGEN_STATIC_LIBRARIES:INTERNAL=
PC_EIGEN_STATIC_LIBRARY_DIRS:INTERNAL=
PC_EIGEN_STATIC_LIBS:INTERNAL=
PC_EIGEN_STATIC_LIBS_L:INTERNAL=
PC_EIGEN_STATIC_LIBS_OTHER:INTERNAL=
PC_EIGEN_STATIC_LIBS_PATHS:INTERNAL=
PC_EIGEN_VERSION:INTERNAL=3.4.0
PC_EIGEN_eigen3_INCLUDEDIR:INTERNAL=
PC_EIGEN_eigen3_LIBDIR:INTERNAL=
PC_EIGEN_eigen3_PREFIX:INTERNAL=
PC_EIGEN_eigen3_VERSION:INTERNAL=
PC_EXPAT_CFLAGS:INTERNAL=
PC_EXPAT_CFLAGS_I:INTERNAL=
PC_EXPAT_CFLAGS_OTHER:INTERNAL=
PC_EXPAT_FOUND:INTERNAL=1
PC_EXPAT_INCLUDEDIR:INTERNAL=/usr/include
PC_EXPAT_INCLUDE_DIRS:INTERNAL=
PC_EXPAT_LDFLAGS:INTERNAL=-L/usr/lib/x86_64-linux-gnu;-lexpat;-lm
PC_EXPAT_LDFLAGS_OTHER:INTERNAL=
PC_EXPAT_LIBDIR:INTERNAL=/usr/lib/x86_64-linux-gnu
PC_EXPAT_LIBRARIES:INTERNAL=expat;m
PC_EXPAT_LIBRARY_DIRS:INTERNAL=/usr/lib/x86_64-linux-gnu
PC_EXPAT_LIBS:INTERNAL=
PC_EXPAT_LIBS_L:INTERNAL=
PC_EXPAT_LIBS_OTHER:INTERNAL=
PC_EXPAT_LIBS_PATHS:INTERNAL=
PC_EXPAT_MODULE_NAME:INTERNAL=expat
PC_EXPAT_PREFIX:INTERNAL=/usr
PC_EXPAT_STATIC_CFLAGS:INTERNAL=
PC_EXPAT_STATIC_CFLAGS_I:INTERNAL=
PC_EXPAT_STATIC_CFLAGS_OTHER:INTERNAL=
PC_EXPAT_STATIC_INCLUDE_DIRS:INTERNAL=
PC_EXPAT_STATIC_LDFLAGS:INTERNAL=-L/usr/lib/x86_64-linux-gnu;-lexpat;-lm
PC_EXPAT_STATIC_LDFLAGS_OTHER:INTERNAL=
PC_EXPAT_STATIC_LIBDIR:INTERNAL=
PC_EXPAT_STATIC_LIBRARIES:INTERNAL=expat;m
PC_EXPAT_STATIC_LIBRARY_DIRS:INTERNAL=/usr/lib/x86_64-linux-gnu
PC_EXPAT_STATIC_LIBS:INTERNAL=
PC_EXPAT_STATIC_LIBS_L:INTERNAL=
PC_EXPAT_STATIC_LIBS_OTHER:INTERNAL=
PC_EXPAT_STATIC_LIBS_PATHS:INTERNAL=
PC_EXPAT_VERSION:INTERNAL=2.4.7
PC_EXPAT_expat_INCLUDEDIR:INTERNAL=
PC_EXPAT_expat_LIBDIR:INTERNAL=
PC_EXPAT_expat_PREFIX:INTERNAL=
PC_EXPAT_expat_VERSION:INTERNAL=
PC_FLANN_CFLAGS:INTERNAL=
PC_FLANN_CFLAGS_I:INTERNAL=
PC_FLANN_CFLAGS_OTHER:INTERNAL=
PC_FLANN_FOUND:INTERNAL=1
PC_FLANN_INCLUDEDIR:INTERNAL=/usr/include
PC_FLANN_INCLUDE_DIRS:INTERNAL=
PC_FLANN_LDFLAGS:INTERNAL=-L/usr/lib/x86_64-linux-gnu;-L/usr/lib;-llz4;-lflann;-lflann_cpp;-llz4
PC_FLANN_LDFLAGS_OTHER:INTERNAL=
PC_FLANN_LIBDIR:INTERNAL=/usr/lib/x86_64-linux-gnu
PC_FLANN_LIBRARIES:INTERNAL=lz4;flann;flann_cpp;lz4
PC_FLANN_LIBRARY_DIRS:INTERNAL=/usr/lib/x86_64-linux-gnu;/usr/lib
PC_FLANN_LIBS:INTERNAL=
PC_FLANN_LIBS_L:INTERNAL=
PC_FLANN_LIBS_OTHER:INTERNAL=
PC_FLANN_LIBS_PATHS:INTERNAL=
PC_FLANN_MODULE_NAME:INTERNAL=flann
PC_FLANN_PREFIX:INTERNAL=/usr
PC_FLANN_STATIC_CFLAGS:INTERNAL=
PC_FLANN_STATIC_CFLAGS_I:INTERNAL=
PC_FLANN_STATIC_CFLAGS_OTHER:INTERNAL=
PC_FLANN_STATIC_INCLUDE_DIRS:INTERNAL=
PC_FLANN_STATIC_LDFLAGS:INTERNAL=-L/usr/lib/x86_64-linux-gnu;-L/usr/lib;-llz4;-lflann;-lflann_cpp;-llz4
PC_FLANN_STATIC_LDFLAGS_OTHER:INTERNAL=
PC_FLANN_STATIC_LIBDIR:INTERNAL=
PC_FLANN_STATIC_LIBRARIES:INTERNAL=lz4;flann;flann_cpp;lz4
PC_FLANN_STATIC_LIBRARY_DIRS:INTERNAL=/usr/lib/x86_64-linux-gnu;/usr/lib
PC_FLANN_STATIC_LIBS:INTERNAL=
PC_FLANN_STATIC_LIBS_L:INTERNAL=
PC_FLANN_STATIC_LIBS_OTHER:INTERNAL=
PC_FLANN_STATIC_LIBS_PATHS:INTERNAL=
PC_FLANN_VERSION:INTERNAL=1.9.1
PC_FLANN_flann_INCLUDEDIR:INTERNAL=
PC_FLANN_flann_LIBDIR:INTERNAL=
PC_FLANN_flann_PREFIX:INTERNAL=
PC_FLANN_flann_VERSION:INTERNAL=
PC_OPENNI2_CFLAGS:INTERNAL=-I/usr/include/openni2
PC_OPENNI2_CFLAGS_I:INTERNAL=
PC_OPENNI2_CFLAGS_OTHER:INTERNAL=
PC_OPENNI2_FOUND:INTERNAL=1
PC_OPENNI2_INCLUDEDIR:INTERNAL=/usr/include/openni2
PC_OPENNI2_INCLUDE_DIRS:INTERNAL=/usr/include/openni2
PC_OPENNI2_LDFLAGS:INTERNAL=-L/usr/lib;-lOpenNI2
PC_OPENNI2_LDFLAGS_OTHER:INTERNAL=
PC_OPENNI2_LIBDIR:INTERNAL=/usr/lib
PC_OPENNI2_LIBRARIES:INTERNAL=OpenNI2
PC_OPENNI2_LIBRARY_DIRS:INTERNAL=/usr/lib
PC_OPENNI2_LIBS:INTERNAL=
PC_OPENNI2_LIBS_L:INTERNAL=
PC_OPENNI2_LIBS_OTHER:INTERNAL=
PC_OPENNI2_LIBS_PATHS:INTERNAL=
PC_OPENNI2_MODULE_NAME:INTERNAL=libopenni2
PC_OPENNI2_PREFIX:INTERNAL=/usr
PC_OPENNI2_STATIC_CFLAGS:INTERNAL=-I/usr/include/openni2
PC_OPENNI2_STATIC_CFLAGS_I:INTERNAL=
PC_OPENNI2_STATIC_CFLAGS_OTHER:INTERNAL=
PC_OPENNI2_STATIC_INCLUDE_DIRS:INTERNAL=/usr/include/openni2
PC_OPENNI2_STATIC_LDFLAGS:INTERNAL=-L/usr/lib;-lOpenNI2
PC_OPENNI2_STATIC_LDFLAGS_OTHER:INTERNAL=
PC_OPENNI2_STATIC_LIBDIR:INTERNAL=
PC_OPENNI2_STATIC_LIBRARIES:INTERNAL=OpenNI2
PC_OPENNI2_STATIC_LIBRARY_DIRS:INTERNAL=/usr/lib
PC_OPENNI2_STATIC_LIBS:INTERNAL=
PC_OPENNI2_STATIC_LIBS_L:INTERNAL=
PC_OPENNI2_STATIC_LIBS_OTHER:INTERNAL=
PC_OPENNI2_STATIC_LIBS_PATHS:INTERNAL=
PC_OPENNI2_VERSION:INTERNAL=2.2.0.3
PC_OPENNI2_libopenni2_INCLUDEDIR:INTERNAL=
PC_OPENNI2_libopenni2_LIBDIR:INTERNAL=
PC_OPENNI2_libopenni2_PREFIX:INTERNAL=
PC_OPENNI2_libopenni2_VERSION:INTERNAL=
PC_OPENNI_CFLAGS:INTERNAL=-I/usr/include/ni
PC_OPENNI_CFLAGS_I:INTERNAL=
PC_OPENNI_CFLAGS_OTHER:INTERNAL=
PC_OPENNI_FOUND:INTERNAL=1
PC_OPENNI_INCLUDEDIR:INTERNAL=/usr/include/ni
PC_OPENNI_INCLUDE_DIRS:INTERNAL=/usr/include/ni
PC_OPENNI_LDFLAGS:INTERNAL=-L/usr/lib;-lOpenNI
PC_OPENNI_LDFLAGS_OTHER:INTERNAL=
PC_OPENNI_LIBDIR:INTERNAL=/usr/lib
PC_OPENNI_LIBRARIES:INTERNAL=OpenNI
PC_OPENNI_LIBRARY_DIRS:INTERNAL=/usr/lib
PC_OPENNI_LIBS:INTERNAL=
PC_OPENNI_LIBS_L:INTERNAL=
PC_OPENNI_LIBS_OTHER:INTERNAL=
PC_OPENNI_LIBS_PATHS:INTERNAL=
PC_OPENNI_MODULE_NAME:INTERNAL=libopenni
PC_OPENNI_PREFIX:INTERNAL=/usr
PC_OPENNI_STATIC_CFLAGS:INTERNAL=-I/usr/include/ni
PC_OPENNI_STATIC_CFLAGS_I:INTERNAL=
PC_OPENNI_STATIC_CFLAGS_OTHER:INTERNAL=
PC_OPENNI_STATIC_INCLUDE_DIRS:INTERNAL=/usr/include/ni
PC_OPENNI_STATIC_LDFLAGS:INTERNAL=-L/usr/lib;-lOpenNI
PC_OPENNI_STATIC_LDFLAGS_OTHER:INTERNAL=
PC_OPENNI_STATIC_LIBDIR:INTERNAL=
PC_OPENNI_STATIC_LIBRARIES:INTERNAL=OpenNI
PC_OPENNI_STATIC_LIBRARY_DIRS:INTERNAL=/usr/lib
PC_OPENNI_STATIC_LIBS:INTERNAL=
PC_OPENNI_STATIC_LIBS_L:INTERNAL=
PC_OPENNI_STATIC_LIBS_OTHER:INTERNAL=
PC_OPENNI_STATIC_LIBS_PATHS:INTERNAL=
PC_OPENNI_VERSION:INTERNAL=1.5.4.0
PC_OPENNI_libopenni_INCLUDEDIR:INTERNAL=
PC_OPENNI_libopenni_LIBDIR:INTERNAL=
PC_OPENNI_libopenni_PREFIX:INTERNAL=
PC_OPENNI_libopenni_VERSION:INTERNAL=
PC_libusb_CFLAGS:INTERNAL=-I/usr/include/libusb-1.0
PC_libusb_CFLAGS_I:INTERNAL=
PC_libusb_CFLAGS_OTHER:INTERNAL=
PC_libusb_FOUND:INTERNAL=1
PC_libusb_INCLUDEDIR:INTERNAL=/usr/include
PC_libusb_INCLUDE_DIRS:INTERNAL=/usr/include/libusb-1.0
PC_libusb_LDFLAGS:INTERNAL=-L/usr/lib/x86_64-linux-gnu;-lusb-1.0
PC_libusb_LDFLAGS_OTHER:INTERNAL=
PC_libusb_LIBDIR:INTERNAL=/usr/lib/x86_64-linux-gnu
PC_libusb_LIBRARIES:INTERNAL=usb-1.0
PC_libusb_LIBRARY_DIRS:INTERNAL=/usr/lib/x86_64-linux-gnu
PC_libusb_LIBS:INTERNAL=
PC_libusb_LIBS_L:INTERNAL=
PC_libusb_LIBS_OTHER:INTERNAL=
PC_libusb_LIBS_PATHS:INTERNAL=
PC_libusb_MODULE_NAME:INTERNAL=libusb-1.0
PC_libusb_PREFIX:INTERNAL=/usr
PC_libusb_STATIC_CFLAGS:INTERNAL=-I/usr/include/libusb-1.0
PC_libusb_STATIC_CFLAGS_I:INTERNAL=
PC_libusb_STATIC_CFLAGS_OTHER:INTERNAL=
PC_libusb_STATIC_INCLUDE_DIRS:INTERNAL=/usr/include/libusb-1.0
PC_libusb_STATIC_LDFLAGS:INTERNAL=-L/usr/lib/x86_64-linux-gnu;-lusb-1.0;-ludev
PC_libusb_STATIC_LDFLAGS_OTHER:INTERNAL=
PC_libusb_STATIC_LIBDIR:INTERNAL=
PC_libusb_STATIC_LIBRARIES:INTERNAL=usb-1.0;udev
PC_libusb_STATIC_LIBRARY_DIRS:INTERNAL=/usr/lib/x86_64-linux-gnu
PC_libusb_STATIC_LIBS:INTERNAL=
PC_libusb_STATIC_LIBS_L:INTERNAL=
PC_libusb_STATIC_LIBS_OTHER:INTERNAL=
PC_libusb_STATIC_LIBS_PATHS:INTERNAL=
PC_libusb_VERSION:INTERNAL=1.0.25
PC_libusb_libusb-1.0_INCLUDEDIR:INTERNAL=
PC_libusb_libusb-1.0_LIBDIR:INTERNAL=
PC_libusb_libusb-1.0_PREFIX:INTERNAL=
PC_libusb_libusb-1.0_VERSION:INTERNAL=
//ADVANCED property for variable: PKG_CONFIG_ARGN
PKG_CONFIG_ARGN-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PKG_CONFIG_EXECUTABLE
PKG_CONFIG_EXECUTABLE-ADVANCED:INTERNAL=1
PKG_FONTCONFIG_CFLAGS:INTERNAL=-I/usr/include/uuid;-I/usr/include/freetype2;-I/usr/include/libpng16
PKG_FONTCONFIG_CFLAGS_I:INTERNAL=
PKG_FONTCONFIG_CFLAGS_OTHER:INTERNAL=
PKG_FONTCONFIG_FOUND:INTERNAL=1
PKG_FONTCONFIG_INCLUDEDIR:INTERNAL=/usr/include
PKG_FONTCONFIG_INCLUDE_DIRS:INTERNAL=/usr/include/uuid;/usr/include/freetype2;/usr/include/libpng16
PKG_FONTCONFIG_LDFLAGS:INTERNAL=-L/usr/lib/x86_64-linux-gnu;-lfontconfig;-lfreetype
PKG_FONTCONFIG_LDFLAGS_OTHER:INTERNAL=
PKG_FONTCONFIG_LIBDIR:INTERNAL=/usr/lib/x86_64-linux-gnu
PKG_FONTCONFIG_LIBRARIES:INTERNAL=fontconfig;freetype
PKG_FONTCONFIG_LIBRARY_DIRS:INTERNAL=/usr/lib/x86_64-linux-gnu
PKG_FONTCONFIG_LIBS:INTERNAL=
PKG_FONTCONFIG_LIBS_L:INTERNAL=
PKG_FONTCONFIG_LIBS_OTHER:INTERNAL=
PKG_FONTCONFIG_LIBS_PATHS:INTERNAL=
PKG_FONTCONFIG_MODULE_NAME:INTERNAL=fontconfig
PKG_FONTCONFIG_PREFIX:INTERNAL=/usr
PKG_FONTCONFIG_STATIC_CFLAGS:INTERNAL=-I/usr/include/uuid;-I/usr/include/freetype2;-I/usr/include/libpng16
PKG_FONTCONFIG_STATIC_CFLAGS_I:INTERNAL=
PKG_FONTCONFIG_STATIC_CFLAGS_OTHER:INTERNAL=
PKG_FONTCONFIG_STATIC_INCLUDE_DIRS:INTERNAL=/usr/include/uuid;/usr/include/freetype2;/usr/include/libpng16
PKG_FONTCONFIG_STATIC_LDFLAGS:INTERNAL=-L/usr/lib/x86_64-linux-gnu;-lfontconfig;-luuid;-lexpat;-lm;-lfreetype;-lpng16;-lm;-lz;-lm;-lz;-lbrotlidec;-lbrotlicommon
PKG_FONTCONFIG_STATIC_LDFLAGS_OTHER:INTERNAL=
PKG_FONTCONFIG_STATIC_LIBDIR:INTERNAL=
PKG_FONTCONFIG_STATIC_LIBRARIES:INTERNAL=fontconfig;uuid;expat;m;freetype;png16;m;z;m;z;brotlidec;brotlicommon
PKG_FONTCONFIG_STATIC_LIBRARY_DIRS:INTERNAL=/usr/lib/x86_64-linux-gnu
PKG_FONTCONFIG_STATIC_LIBS:INTERNAL=
PKG_FONTCONFIG_STATIC_LIBS_L:INTERNAL=
PKG_FONTCONFIG_STATIC_LIBS_OTHER:INTERNAL=
PKG_FONTCONFIG_STATIC_LIBS_PATHS:INTERNAL=
PKG_FONTCONFIG_VERSION:INTERNAL=2.13.1
PKG_FONTCONFIG_fontconfig_INCLUDEDIR:INTERNAL=
PKG_FONTCONFIG_fontconfig_LIBDIR:INTERNAL=
PKG_FONTCONFIG_fontconfig_PREFIX:INTERNAL=
PKG_FONTCONFIG_fontconfig_VERSION:INTERNAL=
//ADVANCED property for variable: PNG_LIBRARY_DEBUG
PNG_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PNG_LIBRARY_RELEASE
PNG_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PNG_PNG_INCLUDE_DIR
PNG_PNG_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: TIFF_INCLUDE_DIR
TIFF_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: TIFF_LIBRARY_DEBUG
TIFF_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: TIFF_LIBRARY_RELEASE
TIFF_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_ICE_INCLUDE_PATH
X11_ICE_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_ICE_LIB
X11_ICE_LIB-ADVANCED:INTERNAL=1
//Have library /usr/lib/x86_64-linux-gnu/libX11.so;/usr/lib/x86_64-linux-gnu/libXext.so
X11_LIB_X11_SOLO:INTERNAL=1
//ADVANCED property for variable: X11_SM_INCLUDE_PATH
X11_SM_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_SM_LIB
X11_SM_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_X11_INCLUDE_PATH
X11_X11_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_X11_LIB
X11_X11_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_X11_xcb_INCLUDE_PATH
X11_X11_xcb_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_X11_xcb_LIB
X11_X11_xcb_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_XRes_INCLUDE_PATH
X11_XRes_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_XRes_LIB
X11_XRes_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_XShm_INCLUDE_PATH
X11_XShm_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_XSync_INCLUDE_PATH
X11_XSync_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xaccessrules_INCLUDE_PATH
X11_Xaccessrules_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xaccessstr_INCLUDE_PATH
X11_Xaccessstr_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xau_INCLUDE_PATH
X11_Xau_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xau_LIB
X11_Xau_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xaw_INCLUDE_PATH
X11_Xaw_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xaw_LIB
X11_Xaw_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xcomposite_INCLUDE_PATH
X11_Xcomposite_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xcomposite_LIB
X11_Xcomposite_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xcursor_INCLUDE_PATH
X11_Xcursor_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xcursor_LIB
X11_Xcursor_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xdamage_INCLUDE_PATH
X11_Xdamage_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xdamage_LIB
X11_Xdamage_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xdmcp_INCLUDE_PATH
X11_Xdmcp_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xdmcp_LIB
X11_Xdmcp_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xext_INCLUDE_PATH
X11_Xext_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xext_LIB
X11_Xext_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xfixes_INCLUDE_PATH
X11_Xfixes_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xfixes_LIB
X11_Xfixes_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xft_INCLUDE_PATH
X11_Xft_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xft_LIB
X11_Xft_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xi_INCLUDE_PATH
X11_Xi_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xi_LIB
X11_Xi_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xinerama_INCLUDE_PATH
X11_Xinerama_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xinerama_LIB
X11_Xinerama_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xkb_INCLUDE_PATH
X11_Xkb_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xkblib_INCLUDE_PATH
X11_Xkblib_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xlib_INCLUDE_PATH
X11_Xlib_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xmu_INCLUDE_PATH
X11_Xmu_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xmu_LIB
X11_Xmu_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xpm_INCLUDE_PATH
X11_Xpm_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xpm_LIB
X11_Xpm_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xrandr_INCLUDE_PATH
X11_Xrandr_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xrandr_LIB
X11_Xrandr_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xrender_INCLUDE_PATH
X11_Xrender_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xrender_LIB
X11_Xrender_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xshape_INCLUDE_PATH
X11_Xshape_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xss_INCLUDE_PATH
X11_Xss_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xss_LIB
X11_Xss_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xt_INCLUDE_PATH
X11_Xt_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xt_LIB
X11_Xt_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xtst_INCLUDE_PATH
X11_Xtst_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xtst_LIB
X11_Xtst_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xutil_INCLUDE_PATH
X11_Xutil_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xv_INCLUDE_PATH
X11_Xv_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xv_LIB
X11_Xv_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xxf86misc_INCLUDE_PATH
X11_Xxf86misc_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xxf86misc_LIB
X11_Xxf86misc_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xxf86vm_INCLUDE_PATH
X11_Xxf86vm_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xxf86vm_LIB
X11_Xxf86vm_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_dpms_INCLUDE_PATH
X11_dpms_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_INCLUDE_PATH
X11_xcb_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_LIB
X11_xcb_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_icccm_INCLUDE_PATH
X11_xcb_icccm_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_icccm_LIB
X11_xcb_icccm_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_util_INCLUDE_PATH
X11_xcb_util_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_util_LIB
X11_xcb_util_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_xfixes_INCLUDE_PATH
X11_xcb_xfixes_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_xfixes_LIB
X11_xcb_xfixes_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_xkb_LIB
X11_xcb_xkb_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xkbcommon_INCLUDE_PATH
X11_xkbcommon_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xkbcommon_LIB
X11_xkbcommon_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xkbcommon_X11_INCLUDE_PATH
X11_xkbcommon_X11_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xkbcommon_X11_LIB
X11_xkbcommon_X11_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xkbfile_INCLUDE_PATH
X11_xkbfile_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xkbfile_LIB
X11_xkbfile_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: ZLIB_INCLUDE_DIR
ZLIB_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: ZLIB_LIBRARY_DEBUG
ZLIB_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: ZLIB_LIBRARY_RELEASE
ZLIB_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
_OPENSSL_CFLAGS:INTERNAL=
_OPENSSL_CFLAGS_I:INTERNAL=
_OPENSSL_CFLAGS_OTHER:INTERNAL=
_OPENSSL_FOUND:INTERNAL=1
_OPENSSL_INCLUDEDIR:INTERNAL=/usr/include
_OPENSSL_INCLUDE_DIRS:INTERNAL=
_OPENSSL_LDFLAGS:INTERNAL=-L/usr/lib/x86_64-linux-gnu;-lssl;-lcrypto
_OPENSSL_LDFLAGS_OTHER:INTERNAL=
_OPENSSL_LIBDIR:INTERNAL=/usr/lib/x86_64-linux-gnu
_OPENSSL_LIBRARIES:INTERNAL=ssl;crypto
_OPENSSL_LIBRARY_DIRS:INTERNAL=/usr/lib/x86_64-linux-gnu
_OPENSSL_LIBS:INTERNAL=
_OPENSSL_LIBS_L:INTERNAL=
_OPENSSL_LIBS_OTHER:INTERNAL=
_OPENSSL_LIBS_PATHS:INTERNAL=
_OPENSSL_MODULE_NAME:INTERNAL=openssl
_OPENSSL_PREFIX:INTERNAL=/usr
_OPENSSL_STATIC_CFLAGS:INTERNAL=
_OPENSSL_STATIC_CFLAGS_I:INTERNAL=
_OPENSSL_STATIC_CFLAGS_OTHER:INTERNAL=
_OPENSSL_STATIC_INCLUDE_DIRS:INTERNAL=
_OPENSSL_STATIC_LDFLAGS:INTERNAL=-L/usr/lib/x86_64-linux-gnu;-lssl;-lcrypto;-ldl;-pthread
_OPENSSL_STATIC_LDFLAGS_OTHER:INTERNAL=-pthread
_OPENSSL_STATIC_LIBDIR:INTERNAL=
_OPENSSL_STATIC_LIBRARIES:INTERNAL=ssl;crypto;dl
_OPENSSL_STATIC_LIBRARY_DIRS:INTERNAL=/usr/lib/x86_64-linux-gnu
_OPENSSL_STATIC_LIBS:INTERNAL=
_OPENSSL_STATIC_LIBS_L:INTERNAL=
_OPENSSL_STATIC_LIBS_OTHER:INTERNAL=
_OPENSSL_STATIC_LIBS_PATHS:INTERNAL=
_OPENSSL_VERSION:INTERNAL=3.0.2
_OPENSSL_openssl_INCLUDEDIR:INTERNAL=
_OPENSSL_openssl_LIBDIR:INTERNAL=
_OPENSSL_openssl_PREFIX:INTERNAL=
_OPENSSL_openssl_VERSION:INTERNAL=
_Python3_EXECUTABLE:INTERNAL=/usr/bin/python3
//Python3 Properties
_Python3_INTERPRETER_PROPERTIES:INTERNAL=Python;3;10;12;64;;cpython-310-x86_64-linux-gnu;/usr/lib/python3.10;/usr/lib/python3.10;/usr/lib/python3/dist-packages;/usr/lib/python3/dist-packages
_Python3_INTERPRETER_SIGNATURE:INTERNAL=0f3e53742e142b1d9e50e4ca5b901dd8
__pkg_config_arguments_PC_EIGEN:INTERNAL=eigen3
__pkg_config_arguments_PC_EXPAT:INTERNAL=QUIET;expat
__pkg_config_arguments_PC_FLANN:INTERNAL=flann
__pkg_config_arguments_PC_OPENNI:INTERNAL=QUIET;libopenni
__pkg_config_arguments_PC_OPENNI2:INTERNAL=QUIET;libopenni2
__pkg_config_arguments_PC_libusb:INTERNAL=libusb-1.0
__pkg_config_arguments_PKG_FONTCONFIG:INTERNAL=QUIET;fontconfig
__pkg_config_arguments__OPENSSL:INTERNAL=QUIET;openssl
__pkg_config_checked_PC_EIGEN:INTERNAL=1
__pkg_config_checked_PC_EXPAT:INTERNAL=1
__pkg_config_checked_PC_FLANN:INTERNAL=1
__pkg_config_checked_PC_OPENNI:INTERNAL=1
__pkg_config_checked_PC_OPENNI2:INTERNAL=1
__pkg_config_checked_PC_libusb:INTERNAL=1
__pkg_config_checked_PKG_FONTCONFIG:INTERNAL=1
__pkg_config_checked__OPENSSL:INTERNAL=1
//ADVANCED property for variable: boost_date_time_DIR
boost_date_time_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: boost_filesystem_DIR
boost_filesystem_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: boost_headers_DIR
boost_headers_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: boost_iostreams_DIR
boost_iostreams_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: boost_serialization_DIR
boost_serialization_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: boost_system_DIR
boost_system_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: double-conversion_INCLUDE_DIR
double-conversion_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: double-conversion_LIBRARY
double-conversion_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: libusb_LIBRARIES
libusb_LIBRARIES-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_PC_EXPAT_expat
pkgcfg_lib_PC_EXPAT_expat-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_PC_EXPAT_m
pkgcfg_lib_PC_EXPAT_m-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_PC_FLANN_flann
pkgcfg_lib_PC_FLANN_flann-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_PC_FLANN_flann_cpp
pkgcfg_lib_PC_FLANN_flann_cpp-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_PC_FLANN_lz4
pkgcfg_lib_PC_FLANN_lz4-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_PC_OPENNI2_OpenNI2
pkgcfg_lib_PC_OPENNI2_OpenNI2-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_PC_OPENNI_OpenNI
pkgcfg_lib_PC_OPENNI_OpenNI-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_PC_libusb_usb-1.0
pkgcfg_lib_PC_libusb_usb-1.0-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_PKG_FONTCONFIG_fontconfig
pkgcfg_lib_PKG_FONTCONFIG_fontconfig-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_PKG_FONTCONFIG_freetype
pkgcfg_lib_PKG_FONTCONFIG_freetype-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib__OPENSSL_crypto
pkgcfg_lib__OPENSSL_crypto-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib__OPENSSL_ssl
pkgcfg_lib__OPENSSL_ssl-ADVANCED:INTERNAL=1
prefix_result:INTERNAL=/usr/lib
//ADVANCED property for variable: utf8cpp_INCLUDE_DIR
utf8cpp_INCLUDE_DIR-ADVANCED:INTERNAL=1

