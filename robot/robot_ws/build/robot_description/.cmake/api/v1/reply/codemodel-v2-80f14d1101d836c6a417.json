{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-76a33587b01299f91923.json", "minimumCMakeVersion": {"string": "3.12"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1]}], "name": "", "projects": [{"directoryIndexes": [0], "name": "robot_description", "targetIndexes": [0, 1]}], "targets": [{"directoryIndex": 0, "id": "robot_description_uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-robot_description_uninstall-c229b34fff1f23e8511f.json", "name": "robot_description_uninstall", "projectIndex": 0}, {"directoryIndex": 0, "id": "uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-uninstall-0fb5bc8c318d32735e32.json", "name": "uninstall", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/robot/robot_ws/build/robot_description", "source": "/home/<USER>/robot/robot_ws/src/robot_description"}, "version": {"major": 2, "minor": 3}}