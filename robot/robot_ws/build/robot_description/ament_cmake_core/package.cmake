set(_AMENT_PACKAGE_NAME "robot_description")
set(robot_description_VERSION "0.0.0")
set(robot_description_MAINTAINER "fml <<EMAIL>>")
set(robot_description_BUILD_DEPENDS "rclcpp" "std_msgs")
set(robot_description_BUILDTOOL_DEPENDS "ament_cmake")
set(robot_description_BUILD_EXPORT_DEPENDS "rclcpp" "std_msgs")
set(robot_description_BUILDTOOL_EXPORT_DEPENDS )
set(robot_description_EXEC_DEPENDS "robot_state_publisher" "gazebo_ros" "xacro" "rclcpp" "std_msgs")
set(robot_description_TEST_DEPENDS "ament_lint_auto" "ament_lint_common")
set(robot_description_GROUP_DEPENDS )
set(robot_description_MEMBER_OF_GROUPS )
set(robot_description_DEPRECATED "")
set(robot_description_EXPORT_TAGS)
list(APPEND robot_description_EXPORT_TAGS "<build_type>ament_cmake</build_type>")
