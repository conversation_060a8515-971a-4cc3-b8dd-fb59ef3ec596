{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-ff93e0faef418f18c311.json", "minimumCMakeVersion": {"string": "3.12"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1]}], "name": "", "projects": [{"directoryIndexes": [0], "name": "robot_navigation2", "targetIndexes": [0, 1]}], "targets": [{"directoryIndex": 0, "id": "robot_navigation2_uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-robot_navigation2_uninstall-cf802fec8ecd96402ee2.json", "name": "robot_navigation2_uninstall", "projectIndex": 0}, {"directoryIndex": 0, "id": "uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-uninstall-71bcd0e7a04adedaaa95.json", "name": "uninstall", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/robot/robot_ws/build/robot_navigation2", "source": "/home/<USER>/robot/robot_ws/src/robot_navigation2"}, "version": {"major": 2, "minor": 3}}